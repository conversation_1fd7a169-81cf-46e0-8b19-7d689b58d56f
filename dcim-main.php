<?php
/**
 * DCIM (Data Center Infrastructure Management) Addon
 * Main Entry Point
 * 
 * This is the main entry point that routes requests to the appropriate
 * module files for better code organization and maintainability.
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

// Include all module files
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';
require_once __DIR__ . '/dcim-ipam.php';
require_once __DIR__ . '/dcim-locations.php';
require_once __DIR__ . '/dcim-servers.php';
require_once __DIR__ . '/dcim-switches.php';
require_once __DIR__ . '/dcim-chassis.php';
require_once __DIR__ . '/dcim-dashboard.php';

/**
 * Main module configuration and information
 */
function dcim_config() {
    return array(
        "name" => "DCIM (Data Center Infrastructure Management)",
        "description" => "Complete data center infrastructure management system with rack visualization, asset tracking, IP address management, and more.",
        "version" => "2.0",
        "author" => "DCIM Team",
        "language" => "english",
        "fields" => array()
    );
}

/**
 * Module activation hook
 */
function dcim_activate() {
    try {
        // Call the activation function from core module
        if (dcim_ensure_tables_exist()) {
            // Run any pending migrations
            dcim_run_migrations();
            return array('status' => 'success', 'description' => 'DCIM module activated successfully with schema version ' . DCIM_SCHEMA_VERSION);
        } else {
            return array('status' => 'error', 'description' => 'Failed to create DCIM database tables. Check error logs for details.');
        }
    } catch (Exception $e) {
        error_log("DCIM: Activation failed - " . $e->getMessage());
        return array('status' => 'error', 'description' => 'DCIM activation failed: ' . $e->getMessage());
    }
}

/**
 * Module deactivation hook
 */
function dcim_deactivate() {
    return array('status' => 'success', 'description' => 'DCIM module deactivated successfully');
}

/**
 * Main module output function - handles routing to appropriate functions
 */
function dcim_output($vars) {
    $modulelink = $vars['modulelink'];
    $action = $_REQUEST['action'] ?? 'dashboard';
    
    // Initialize core functionality
    dcim_ensure_tables_exist();

    // --- FIX: AJAX modal for Add Switch ---
    if (isset($_GET['ajax']) && (
        (isset($_GET['action']) && $_GET['action'] === 'switches_manage') ||
        (isset($_POST['action']) && $_POST['action'] === 'switches_manage')
    )) {
        // Only output the modal HTML for AJAX modal
        dcim_manage_switches($modulelink);
        return;
    }
    // --- END FIX ---

    // --- FIX: AJAX modal for Add Chassis ---
    if (isset($_GET['ajax']) && (
        (isset($_GET['action']) && $_GET['action'] === 'chassies_manage') ||
        (isset($_POST['action']) && $_POST['action'] === 'chassies_manage')
    )) {
        // Only output the modal HTML for AJAX modal
        dcim_manage_chassies($modulelink);
        return;
    }
    // --- END FIX ---

    // Handle AJAX requests first (other AJAX actions)
    if (isset($_GET['ajax'])) {
        dcim_handle_ajax_requests();
        return;
    }
    
    // Handle sample data creation
    if (isset($_GET['create_sample']) && $_GET['create_sample'] == 'true') {
        dcim_create_sample_data();
        header("Location: " . $modulelink);
        exit;
    }
    
    // Handle device assignment
    if ($action == 'assign_device') {
        dcim_handle_device_assignment();
        return;
    }
    
    // Route to appropriate function based on action
    switch ($action) {
        // Dashboard routes
        case 'dashboard':
            dcim_dashboard($modulelink);
            break;
            
        // Fallback for unknown actions
        default:
            if ($action !== 'dashboard') {
                echo '<div class="alert alert-warning">Unknown action: ' . htmlspecialchars($action) . '</div>';
            }
            dcim_dashboard($modulelink);
            break;
            
        case 'rack_view':
            dcim_modern_rack_view($modulelink);
            break;
            
        case 'servers_table':
            dcim_servers_table($modulelink);
            break;
            
        // Location management routes
        case 'locations':
        case 'manage_locations':
            dcim_manage_locations($modulelink);
            break;
            
        case 'manage_racks':
            dcim_manage_racks($modulelink);
            break;
            
        // Server management routes  
        case 'servers':
        case 'servers_manage':
            dcim_manage_servers($modulelink);
            break;
            
        case 'servers_bulk':
            dcim_manage_servers($modulelink);
            break;
            
        // Switch management routes
        case 'switches':
        case 'switches_manage':
            dcim_manage_switches($modulelink);
            break;
            
        case 'switches_table':
            dcim_switches_table($modulelink);
            break;
            
        case 'switches_bulk':
            dcim_manage_switches($modulelink);
            break;
            
        // Chassis management routes
        case 'chassis':
        case 'chassis_manage':
            dcim_manage_chassies($modulelink);
            break;

        case 'chassies':
        case 'chassies_table':
            dcim_chassies_table($modulelink);
            break;

        case 'chassies_manage':
            dcim_manage_chassies($modulelink);
            break;

        case 'chassis_bulk':
            dcim_manage_chassies($modulelink);
            break;
            
        // IPAM routes
        case 'ipam':
        case 'ipam_dashboard':
            dcim_ipam_dashboard($modulelink);
            break;
            
        case 'ipam_subnets':
            dcim_manage_subnets($modulelink);
            break;
            
        case 'ipam_create_subnet':
            dcim_manage_subnets($modulelink);
            break;
            
        case 'ipam_manage_subnet':
            dcim_manage_subnets($modulelink);
            break;
            
        case 'ipam_assign_ip':
        case 'ipam_ips':
            dcim_manage_ips($modulelink);
            break;

        case 'ipam_allocation':
            dcim_ip_allocation($modulelink);
            break;
            
        // Database maintenance routes
        case 'database_maintenance':
            // Show database maintenance within the module instead of redirecting
            dcim_database_maintenance_page($modulelink);
            break;
    }
}

/**
 * Handle device assignment for rack visualization
 */
function dcim_handle_device_assignment() {
    if (!isset($_GET['device_id'])) {
        return;
    }
    
    $device_id = (int)$_GET['device_id'];
    $rack_id = !empty($_GET['rack_id']) ? (int)$_GET['rack_id'] : null;
    $unit = !empty($_GET['unit']) ? (int)$_GET['unit'] : null;
    
    try {
        if ($rack_id && $unit) {
            // Assign device to rack
            Capsule::table('dcim_servers')
                ->where('id', $device_id)
                ->update([
                    'rack_id' => $rack_id,
                    'start_unit' => $unit,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } else {
            // Unassign device from rack
            Capsule::table('dcim_servers')
                ->where('id', $device_id)
                ->update([
                    'rack_id' => null,
                    'start_unit' => null,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
        
        // Redirect back to rack view
        if (isset($_SERVER['HTTP_REFERER'])) {
            header("Location: " . $_SERVER['HTTP_REFERER']);
        } else {
            header("Location: " . $_GET['modulelink']);
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error updating device: ' . $e->getMessage() . '</div>';
    }
    
    exit;
}

/**
 * Client area output - shows assigned servers for the logged-in client
 */
function dcim_clientarea($vars) {
    $modulelink = $vars['modulelink'];
    
    // Get client ID
    $client_id = $_SESSION['uid'] ?? null;
    if (!$client_id) {
        return array(
            'pagetitle' => 'DCIM - Access Denied',
            'breadcrumb' => array('index.php?m=dcim' => 'DCIM'),
            'templatefile' => 'access_denied',
            'requirelogin' => true,
            'vars' => array(
                'message' => 'You must be logged in to access this page.'
            ),
        );
    }
    
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    try {
        // Get servers assigned to this client
        $servers = Capsule::table('dcim_servers')
            ->leftJoin('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->where('dcim_servers.client_id', $client_id)
            ->select('dcim_servers.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_servers.name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching client servers - " . $e->getMessage());
        $servers = collect([]);
    }
    
    return array(
        'pagetitle' => 'DCIM - My Servers',
        'breadcrumb' => array('index.php?m=dcim' => 'DCIM'),
        'templatefile' => 'clientarea',
        'requirelogin' => true,
        'vars' => array(
            'servers' => $servers,
            'modulelink' => $modulelink,
        ),
    );
}

/**
 * Legacy compatibility functions - redirect to new module structure
 */

// Legacy function redirects - these functions are now defined in their respective modules
// dcim_ensure_tables_exist() is defined in dcim-core.php
// dcim_create_sample_data() is defined in dcim-core.php  
// dcim_handle_ajax_requests() is defined in dcim-core.php
// dcim_generate_sidebar() is defined in dcim-sidebar.php
// dcim_generate_sidebar_javascript() is defined in dcim-sidebar.php

/**
 * Hook functions for WHMCS integration
 */

// Hook to display servers in client area
add_hook('ClientAreaPage', 1, function($vars) {
    if ($vars['templatefile'] == 'clientarea' && isset($_GET['m']) && $_GET['m'] == 'dcim') {
        return dcim_clientarea($vars);
    }
});

// Hook to validate IP addresses when creating products
add_hook('ShoppingCartValidateCheckout', 1, function($vars) {
    // Add any DCIM-specific validation here
    return array();
});

// Daily cron hook for maintenance tasks
add_hook('DailyCronJob', 1, function($vars) {
    try {
        // Clean up old logs, update statistics, etc.
        dcim_daily_maintenance();
    } catch (Exception $e) {
        error_log("DCIM: Daily maintenance error - " . $e->getMessage());
    }
});

/**
 * Daily maintenance tasks
 */
function dcim_daily_maintenance() {
    try {
        // Run database integrity check
        $issues = dcim_check_database_integrity();
        if (!empty($issues)) {
            error_log("DCIM: Daily maintenance found " . count($issues) . " database integrity issues");
            
            // Attempt to repair minor issues automatically
            if (dcim_repair_data_integrity()) {
                error_log("DCIM: Daily maintenance successfully repaired data integrity issues");
            } else {
                error_log("DCIM: Daily maintenance could not repair all data integrity issues");
            }
        }
        
        // Update calculated fields and statistics
        dcim_update_calculated_fields();
        
        // Check for schema updates
        $current_version = dcim_get_schema_version();
        if (version_compare($current_version, DCIM_SCHEMA_VERSION, '<')) {
            error_log("DCIM: Schema update available - current: {$current_version}, target: " . DCIM_SCHEMA_VERSION);
        }
        
        // Log maintenance completion
        error_log("DCIM: Daily maintenance completed successfully");
    } catch (Exception $e) {
        error_log("DCIM: Daily maintenance failed - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Error handling and logging utilities
 */
function dcim_log_error($message, $context = array()) {
    $log_message = "DCIM: " . $message;
    if (!empty($context)) {
        $log_message .= " | Context: " . json_encode($context);
    }
    error_log($log_message);
}

function dcim_log_info($message, $context = array()) {
    $log_message = "DCIM: " . $message;
    if (!empty($context)) {
        $log_message .= " | Context: " . json_encode($context);
    }
    error_log($log_message);
}

/**
 * Utility functions for data validation
 */
function dcim_validate_ip_address($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP) !== false;
}

function dcim_validate_hostname($hostname) {
    return preg_match('/^[a-zA-Z0-9.-]+$/', $hostname) && strlen($hostname) <= 253;
}

function dcim_validate_rack_unit($unit, $max_units = 42) {
    return is_numeric($unit) && $unit >= 1 && $unit <= $max_units;
}

/**
 * Security functions
 */
function dcim_sanitize_input($input) {
    if (is_array($input)) {
        return array_map('dcim_sanitize_input', $input);
    }
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function dcim_check_admin_permission() {
    if (!isset($_SESSION['adminid'])) {
        die('Access denied: Admin login required');
    }
}

/**
 * Export and import functions
 */
function dcim_export_data($format = 'json') {
    dcim_check_admin_permission();
    
    try {
        $data = array(
            'locations' => Capsule::table('dcim_locations')->get()->toArray(),
            'racks' => Capsule::table('dcim_racks')->get()->toArray(),
            'servers' => Capsule::table('dcim_servers')->get()->toArray(),
            'switches' => Capsule::table('dcim_switches')->get()->toArray(),
            'chassis' => Capsule::table('dcim_chassis')->get()->toArray(),
            'subnets' => Capsule::table('dcim_subnets')->get()->toArray(),
            'ip_addresses' => Capsule::table('dcim_ip_addresses')->get()->toArray(),
        );
        
        switch ($format) {
            case 'json':
                header('Content-Type: application/json');
                header('Content-Disposition: attachment; filename="dcim_export_' . date('Y-m-d_H-i-s') . '.json"');
                echo json_encode($data, JSON_PRETTY_PRINT);
                break;
                
            case 'csv':
                // Implement CSV export if needed
                break;
                
            default:
                throw new Exception('Unsupported export format');
        }
        
        exit;
    } catch (Exception $e) {
        dcim_log_error('Export failed', array('error' => $e->getMessage()));
        echo 'Export failed: ' . $e->getMessage();
    }
}

?> 