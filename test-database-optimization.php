<?php
/**
 * DCIM Database Optimization Test Script
 * 
 * This script tests the new database optimization and validation features
 * implemented in Task 1: Core Infrastructure Setup and Database Optimization
 */

if (!defined("WHMCS")) {
    // Allow direct access for testing
    require_once '../../../init.php';
}

use WHMCS\Database\Capsule;

// Include core functions
require_once __DIR__ . '/dcim-core.php';

echo '<h1>DCIM Database Optimization Test</h1>';
echo '<p>Testing the enhanced database optimization and validation features.</p>';

try {
    echo '<h2>1. Testing Schema Version Management</h2>';
    $current_version = dcim_get_schema_version();
    $target_version = DCIM_SCHEMA_VERSION;
    echo "<p>Current Version: {$current_version}</p>";
    echo "<p>Target Version: {$target_version}</p>";
    
    if (version_compare($current_version, $target_version, '<')) {
        echo '<p style="color: orange;">⚠️ Migration needed</p>';
    } else {
        echo '<p style="color: green;">✅ Schema is up to date</p>';
    }
    
    echo '<h2>2. Testing Database Migration System</h2>';
    echo '<p>Running migration check...</p>';
    $migration_result = dcim_run_migrations();
    if ($migration_result) {
        echo '<p style="color: green;">✅ Migration system working correctly</p>';
    } else {
        echo '<p style="color: red;">❌ Migration system failed</p>';
    }
    
    echo '<h2>3. Testing Integrity Audit Table</h2>';
    if (Capsule::schema()->hasTable('dcim_integrity_audit')) {
        echo '<p style="color: green;">✅ Integrity audit table exists</p>';
        $audit_count = Capsule::table('dcim_integrity_audit')->count();
        echo "<p>Audit records: {$audit_count}</p>";
    } else {
        echo '<p style="color: red;">❌ Integrity audit table missing</p>';
    }
    
    echo '<h2>4. Testing Database Integrity Check</h2>';
    echo '<p>Running comprehensive integrity check...</p>';
    $issues = dcim_check_database_integrity();
    if (empty($issues)) {
        echo '<p style="color: green;">✅ No integrity issues found</p>';
    } else {
        echo '<p style="color: orange;">⚠️ Found ' . count($issues) . ' integrity issues:</p>';
        echo '<ul>';
        foreach ($issues as $issue) {
            echo '<li>' . htmlspecialchars($issue) . '</li>';
        }
        echo '</ul>';
    }
    
    echo '<h2>5. Testing Data Validation Functions</h2>';
    
    // Test CIDR validation
    $test_cidrs = [
        '192.168.1.0/24' => true,
        '10.0.0.0/8' => true,
        '172.16.0.0/16' => true,
        '192.168.1.0/33' => false,
        'invalid' => false,
        '192.168.1.0' => false
    ];
    
    echo '<h3>CIDR Validation Test:</h3>';
    foreach ($test_cidrs as $cidr => $expected) {
        $result = dcim_validate_cidr($cidr);
        $status = ($result === $expected) ? '✅' : '❌';
        echo "<p>{$status} {$cidr} - Expected: " . ($expected ? 'valid' : 'invalid') . ", Got: " . ($result ? 'valid' : 'invalid') . "</p>";
    }
    
    echo '<h2>6. Testing Validation Report Generation</h2>';
    $report = dcim_get_validation_report();
    if ($report) {
        echo '<p style="color: green;">✅ Validation report generated successfully</p>';
        echo '<p>Total issues: ' . $report['summary']['total_issues'] . '</p>';
        if (!empty($report['issues_by_table'])) {
            echo '<p>Issues by table:</p>';
            echo '<ul>';
            foreach ($report['issues_by_table'] as $table => $count) {
                echo "<li>{$table}: {$count} issues</li>";
            }
            echo '</ul>';
        }
    } else {
        echo '<p style="color: red;">❌ Failed to generate validation report</p>';
    }
    
    echo '<h2>7. Testing Database Indexes</h2>';
    echo '<p>Checking if performance indexes exist...</p>';
    
    // Test some key indexes
    $index_tests = [
        'dcim_servers' => ['idx_dcim_servers_rack_id', 'idx_dcim_servers_status'],
        'dcim_ip_addresses' => ['idx_dcim_ip_addresses_subnet_id', 'idx_dcim_ip_addresses_status'],
        'dcim_subnets' => ['idx_dcim_subnets_location_id', 'idx_dcim_subnets_status']
    ];
    
    foreach ($index_tests as $table => $indexes) {
        if (Capsule::schema()->hasTable($table)) {
            echo "<h4>Table: {$table}</h4>";
            foreach ($indexes as $index) {
                try {
                    // Try to use the index in a query to see if it exists
                    Capsule::statement("SHOW INDEX FROM {$table} WHERE Key_name = '{$index}'");
                    echo "<p style='color: green;'>✅ Index {$index} exists</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ Index {$index} may not exist or database doesn't support SHOW INDEX</p>";
                }
            }
        }
    }
    
    echo '<h2>8. Testing Foreign Key Constraints</h2>';
    echo '<p>Checking foreign key relationships...</p>';
    
    // Test foreign key relationships by checking referential integrity
    $fk_tests = [
        'dcim_racks -> dcim_locations' => [
            'child_table' => 'dcim_racks',
            'child_column' => 'location_id',
            'parent_table' => 'dcim_locations',
            'parent_column' => 'id'
        ],
        'dcim_servers -> dcim_racks' => [
            'child_table' => 'dcim_servers',
            'child_column' => 'rack_id',
            'parent_table' => 'dcim_racks',
            'parent_column' => 'id'
        ],
        'dcim_ip_addresses -> dcim_subnets' => [
            'child_table' => 'dcim_ip_addresses',
            'child_column' => 'subnet_id',
            'parent_table' => 'dcim_subnets',
            'parent_column' => 'id'
        ]
    ];
    
    foreach ($fk_tests as $relationship => $config) {
        if (Capsule::schema()->hasTable($config['child_table']) && 
            Capsule::schema()->hasTable($config['parent_table'])) {
            
            // Check for orphaned records
            $orphaned = Capsule::table($config['child_table'])
                ->leftJoin($config['parent_table'], 
                    $config['child_table'] . '.' . $config['child_column'], 
                    '=', 
                    $config['parent_table'] . '.' . $config['parent_column'])
                ->whereNotNull($config['child_table'] . '.' . $config['child_column'])
                ->whereNull($config['parent_table'] . '.' . $config['parent_column'])
                ->count();
                
            if ($orphaned === 0) {
                echo "<p style='color: green;'>✅ {$relationship} - No orphaned records</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ {$relationship} - Found {$orphaned} orphaned records</p>";
            }
        }
    }
    
    echo '<h2>9. Performance Test</h2>';
    echo '<p>Testing query performance with indexes...</p>';
    
    if (Capsule::schema()->hasTable('dcim_servers')) {
        $start_time = microtime(true);
        $server_count = Capsule::table('dcim_servers')->where('status', 'online')->count();
        $end_time = microtime(true);
        $query_time = round(($end_time - $start_time) * 1000, 2);
        echo "<p>Server status query: {$server_count} results in {$query_time}ms</p>";
    }
    
    if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
        $start_time = microtime(true);
        $ip_count = Capsule::table('dcim_ip_addresses')->where('status', 'assigned')->count();
        $end_time = microtime(true);
        $query_time = round(($end_time - $start_time) * 1000, 2);
        echo "<p>IP address status query: {$ip_count} results in {$query_time}ms</p>";
    }
    
    echo '<h2>✅ Database Optimization Test Complete</h2>';
    echo '<p style="color: green;">All core infrastructure setup and database optimization features have been tested.</p>';
    
} catch (Exception $e) {
    echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
    echo '<h3>Test Error</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<p>Stack trace:</p>';
    echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    echo '</div>';
}

echo '<hr>';
echo '<p><a href="dcim-database-maintenance.php">Go to Database Maintenance</a> | <a href="?m=dcim">Return to DCIM</a></p>';
?>