<?php
/**
 * DCIM Core Functions - Configuration, Database Setup & Utilities
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * Core configuration and database functions for DCIM addon
 * Note: dcim_config() function is defined in dcim-main.php to avoid conflicts
 */

/**
 * Database schema version management
 */
define('DCIM_SCHEMA_VERSION', '2.1.0');

/**
 * Get current database schema version
 */
function dcim_get_schema_version() {
    try {
        if (!Capsule::schema()->hasTable('dcim_schema_version')) {
            return '1.0.0'; // Default version for existing installations
        }
        
        $version = Capsule::table('dcim_schema_version')
            ->orderBy('id', 'desc')
            ->first();
            
        return $version ? $version->version : '1.0.0';
    } catch (Exception $e) {
        error_log("DCIM: Error getting schema version - " . $e->getMessage());
        return '1.0.0';
    }
}

/**
 * Update schema version
 */
function dcim_update_schema_version($version) {
    try {
        // Ensure schema version table exists
        if (!Capsule::schema()->hasTable('dcim_schema_version')) {
            Capsule::schema()->create('dcim_schema_version', function ($table) {
                $table->increments('id');
                $table->string('version');
                $table->timestamp('applied_at')->useCurrent();
            });
        }
        
        Capsule::table('dcim_schema_version')->insert([
            'version' => $version,
            'applied_at' => date('Y-m-d H:i:s')
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error updating schema version - " . $e->getMessage());
        return false;
    }
}

/**
 * Run database migrations
 */
function dcim_run_migrations() {
    $current_version = dcim_get_schema_version();
    $target_version = DCIM_SCHEMA_VERSION;
    
    if (version_compare($current_version, $target_version, '>=')) {
        return true; // Already up to date
    }
    
    try {
        // Migration from 1.0.0 to 2.0.0
        if (version_compare($current_version, '2.0.0', '<')) {
            dcim_migrate_to_v2();
            dcim_update_schema_version('2.0.0');
        }
        
        // Migration from 2.0.0 to 2.1.0 - Enhanced validation and integrity
        if (version_compare($current_version, '2.1.0', '<')) {
            dcim_migrate_to_v2_1();
            dcim_update_schema_version('2.1.0');
        }
        
        return true;
    } catch (Exception $e) {
        error_log("DCIM: Migration failed - " . $e->getMessage());
        return false;
    }
}

/**
 * Migration to version 2.0.0 - Enhanced schema with proper constraints and indexes
 */
function dcim_migrate_to_v2() {
    // Add missing indexes for performance
    dcim_add_database_indexes();
    
    // Ensure foreign key constraints are properly set
    dcim_ensure_foreign_key_constraints();
    
    // Add any missing columns
    dcim_add_missing_columns();
    
    // Validate and repair data integrity
    dcim_repair_data_integrity();
}

/**
 * Migration to version 2.1.0 - Enhanced validation and integrity checking
 */
function dcim_migrate_to_v2_1() {
    // Create database integrity audit table
    dcim_create_integrity_audit_table();
    
    // Add enhanced validation triggers and constraints
    dcim_add_enhanced_validation_constraints();
    
    // Create automated integrity checking procedures
    dcim_create_integrity_check_procedures();
    
    // Run comprehensive data validation and repair
    dcim_comprehensive_data_validation();
    
    // Add performance monitoring indexes
    dcim_add_performance_monitoring_indexes();
}

/**
 * Add database indexes for performance optimization
 */
function dcim_add_database_indexes() {
    try {
        // Add indexes to locations table
        if (Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_locations_status ON dcim_locations(status)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_locations_city_country ON dcim_locations(city, country)');
        }
        
        // Add indexes to racks table
        if (Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_racks_location_id ON dcim_racks(location_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_racks_status ON dcim_racks(status)');
        }
        
        // Add indexes to servers table
        if (Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_rack_id ON dcim_servers(rack_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_client_id ON dcim_servers(client_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_service_id ON dcim_servers(service_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_status ON dcim_servers(status)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_hostname ON dcim_servers(hostname)');
        }
        
        // Add indexes to switches table
        if (Capsule::schema()->hasTable('dcim_switches')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_switches_rack_id ON dcim_switches(rack_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_switches_client_id ON dcim_switches(client_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_switches_status ON dcim_switches(status)');
        }
        
        // Add indexes to chassis table
        if (Capsule::schema()->hasTable('dcim_chassies')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_chassies_rack_id ON dcim_chassies(rack_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_chassies_client_id ON dcim_chassies(client_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_chassies_status ON dcim_chassies(status)');
        }
        
        // Add indexes to subnets table
        if (Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_subnets_location_id ON dcim_subnets(location_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_subnets_status ON dcim_subnets(status)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_subnets_type ON dcim_subnets(subnet_type)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_subnets_version ON dcim_subnets(ip_version)');
        }
        
        // Add indexes to IP addresses table
        if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_ip_addresses_subnet_id ON dcim_ip_addresses(subnet_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_ip_addresses_status ON dcim_ip_addresses(status)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_ip_addresses_ip ON dcim_ip_addresses(ip_address)');
        }
        
        // Add indexes to IP assignments table
        if (Capsule::schema()->hasTable('dcim_ip_assignments')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_ip_assignments_device ON dcim_ip_assignments(device_type, device_id)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_ip_assignments_ip_id ON dcim_ip_assignments(ip_address_id)');
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error adding database indexes - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Ensure all foreign key constraints are properly set
 */
function dcim_ensure_foreign_key_constraints() {
    try {
        // Note: Laravel/Eloquent doesn't provide easy way to check existing foreign keys
        // We'll use raw SQL to ensure constraints exist
        
        // Check and add foreign keys for racks table
        if (Capsule::schema()->hasTable('dcim_racks') && Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::statement('
                ALTER TABLE dcim_racks 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_racks_location_id 
                FOREIGN KEY (location_id) REFERENCES dcim_locations(id) ON DELETE CASCADE
            ');
        }
        
        // Check and add foreign keys for servers table
        if (Capsule::schema()->hasTable('dcim_servers') && Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::statement('
                ALTER TABLE dcim_servers 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_servers_rack_id 
                FOREIGN KEY (rack_id) REFERENCES dcim_racks(id) ON DELETE SET NULL
            ');
        }
        
        // Check and add foreign keys for switches table
        if (Capsule::schema()->hasTable('dcim_switches') && Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::statement('
                ALTER TABLE dcim_switches 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_switches_rack_id 
                FOREIGN KEY (rack_id) REFERENCES dcim_racks(id) ON DELETE SET NULL
            ');
        }
        
        // Check and add foreign keys for chassis table
        if (Capsule::schema()->hasTable('dcim_chassies') && Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::statement('
                ALTER TABLE dcim_chassies 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_chassies_rack_id 
                FOREIGN KEY (rack_id) REFERENCES dcim_racks(id) ON DELETE SET NULL
            ');
        }
        
        // Check and add foreign keys for subnets table
        if (Capsule::schema()->hasTable('dcim_subnets') && Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::statement('
                ALTER TABLE dcim_subnets 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_subnets_location_id 
                FOREIGN KEY (location_id) REFERENCES dcim_locations(id) ON DELETE SET NULL
            ');
        }
        
        // Check and add foreign keys for IP addresses table
        if (Capsule::schema()->hasTable('dcim_ip_addresses') && Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::statement('
                ALTER TABLE dcim_ip_addresses 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_ip_addresses_subnet_id 
                FOREIGN KEY (subnet_id) REFERENCES dcim_subnets(id) ON DELETE CASCADE
            ');
        }
        
        // Check and add foreign keys for IP assignments table
        if (Capsule::schema()->hasTable('dcim_ip_assignments') && Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::statement('
                ALTER TABLE dcim_ip_assignments 
                ADD CONSTRAINT IF NOT EXISTS fk_dcim_ip_assignments_ip_address_id 
                FOREIGN KEY (ip_address_id) REFERENCES dcim_ip_addresses(id) ON DELETE CASCADE
            ');
        }
        
    } catch (Exception $e) {
        // Foreign key constraints might fail if using SQLite or if constraints already exist
        // Log the error but don't fail the migration
        error_log("DCIM: Warning - Foreign key constraint setup: " . $e->getMessage());
    }
}

/**
 * Add any missing columns to existing tables
 */
function dcim_add_missing_columns() {
    try {
        // Add missing columns to racks table
        if (Capsule::schema()->hasTable('dcim_racks')) {
            if (!Capsule::schema()->hasColumn('dcim_racks', 'row')) {
                Capsule::schema()->table('dcim_racks', function ($table) {
                    $table->string('row')->nullable()->after('name');
                    $table->string('position')->nullable()->after('row');
                });
            }
            if (!Capsule::schema()->hasColumn('dcim_racks', 'pdu_a')) {
                Capsule::schema()->table('dcim_racks', function ($table) {
                    $table->string('pdu_a')->nullable()->after('power_capacity');
                    $table->string('pdu_b')->nullable()->after('pdu_a');
                });
            }
        }
        
        // Ensure subnets table has all required columns
        if (Capsule::schema()->hasTable('dcim_subnets')) {
            if (!Capsule::schema()->hasColumn('dcim_subnets', 'subnet')) {
                Capsule::schema()->table('dcim_subnets', function ($table) {
                    $table->string('subnet')->after('location_id');
                });
                
                // Populate subnet column from existing network and prefix_length
                $subnets = Capsule::table('dcim_subnets')->get();
                foreach ($subnets as $subnet) {
                    if (empty($subnet->subnet) && !empty($subnet->network) && !empty($subnet->prefix_length)) {
                        Capsule::table('dcim_subnets')
                            ->where('id', $subnet->id)
                            ->update(['subnet' => $subnet->network . '/' . $subnet->prefix_length]);
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error adding missing columns - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Ensure all database tables exist with proper schema
 */
function dcim_ensure_tables_exist() {
    try {
        // Run migrations first
        if (!dcim_run_migrations()) {
            error_log("DCIM: Migration failed, attempting table creation");
        }
        
        // Check if we need to update existing tables with new schema
        if (Capsule::schema()->hasTable('dcim_subnets')) {
            // Check if the subnet column exists (new schema)
            if (!Capsule::schema()->hasColumn('dcim_subnets', 'subnet')) {
                // Old schema detected, need to recreate tables
                error_log("DCIM: Old schema detected, tables need to be recreated");
                return false; // Let the reset script handle this
            }
        }

        // Check and create locations table
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::schema()->create('dcim_locations', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->text('address')->nullable();
                $table->string('city')->nullable();
                $table->string('country')->nullable();
                $table->string('contact_name')->nullable();
                $table->string('contact_email')->nullable();
                $table->string('contact_phone')->nullable();
                $table->integer('total_power_capacity')->default(0);
                $table->string('power_unit')->default('Watts');
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
            });
        }

        // Check and create racks table
        if (!Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::schema()->create('dcim_racks', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('row')->nullable();
                $table->string('position')->nullable();
                $table->integer('units')->default(42);
                $table->integer('power_capacity')->default(0);
                $table->string('pdu_a')->nullable();
                $table->string('pdu_b')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'maintenance', 'inactive'])->default('active');
                $table->timestamps();
                
                // Add foreign key only if locations table exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
                }
            });
        }

        // Check and create CPU models table
        if (!Capsule::schema()->hasTable('dcim_cpu_models')) {
            Capsule::schema()->create('dcim_cpu_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->string('cores')->nullable();
                $table->string('frequency')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create RAM configurations table
        if (!Capsule::schema()->hasTable('dcim_ram_configs')) {
            Capsule::schema()->create('dcim_ram_configs', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('size')->nullable();
                $table->string('type')->nullable();
                $table->string('speed')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create switch models table
        if (!Capsule::schema()->hasTable('dcim_switch_models')) {
            Capsule::schema()->create('dcim_switch_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->string('model_number')->nullable();
                $table->integer('ports')->default(24);
                $table->string('port_speed')->nullable(); // 1G, 10G, etc.
                $table->string('switch_type')->nullable(); // core, edge, tor, management
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create chassis models table
        if (!Capsule::schema()->hasTable('dcim_chassis_models')) {
            Capsule::schema()->create('dcim_chassis_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create servers table
        if (!Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::schema()->create('dcim_servers', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(1);
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->text('specifications')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->string('ip_address')->nullable();
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create switches table
        if (!Capsule::schema()->hasTable('dcim_switches')) {
            Capsule::schema()->create('dcim_switches', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(1);
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('ports')->default(24);
                $table->string('switch_type')->nullable(); // core, edge, tor, management
                $table->string('management_ip')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create chassies table
        if (!Capsule::schema()->hasTable('dcim_chassies')) {
            Capsule::schema()->create('dcim_chassies', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(10); // Chassis are typically larger
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('slots')->default(8); // Blade slots
                $table->string('chassis_type')->nullable(); // blade, storage, network
                $table->string('management_ip')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create IPAM subnets table
        if (!Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::schema()->create('dcim_subnets', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('subnet'); // Full CIDR notation, e.g., ***********/24
                $table->string('network'); // Network address, e.g., ***********
                $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
                $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
                $table->string('country')->nullable();
                $table->string('city')->nullable();
                $table->boolean('is_public')->default(false);
                $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
                $table->string('gateway')->nullable();
                $table->string('dns_primary')->nullable();
                $table->string('dns_secondary')->nullable();
                $table->string('vlan_id')->nullable();
                $table->text('note')->nullable();
                $table->enum('status', ['Available', 'Parent of Allocated Subnet', 'Reserved', 'Deprecated'])->default('Available');
                $table->timestamps();

                // Add foreign key to locations if it exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
                }

                // Add unique constraint on subnet CIDR
                $table->unique(['subnet']);
            });
        }

        // Check and create IPAM IP addresses table
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::schema()->create('dcim_ip_addresses', function ($table) {
                $table->increments('id');
                $table->integer('subnet_id')->unsigned();
                $table->string('ip_address');
                $table->string('hostname')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
                $table->timestamps();
                
                // Add foreign key to subnets
                $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
                
                // Ensure each IP is unique within a subnet
                $table->unique(['subnet_id', 'ip_address']);
            });
        }

        // Check and create IPAM IP assignments table (links IPs to devices)
        if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
            Capsule::schema()->create('dcim_ip_assignments', function ($table) {
                $table->increments('id');
                $table->integer('ip_address_id')->unsigned();
                $table->enum('device_type', ['server', 'switch', 'chassis']);
                $table->integer('device_id')->unsigned();
                $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
                $table->timestamps();
                
                // Add foreign key to IP addresses
                $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');
                
                // Ensure one IP can't be assigned multiple times to the same interface
                $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
            });
        }

        // Add foreign keys if tables exist but keys are missing
        dcim_add_missing_foreign_keys();

        // Populate default data
        dcim_populate_default_cpu_models();
        dcim_populate_default_ram_configs();
        dcim_populate_default_switch_models();
        dcim_populate_default_chassis_models();

        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error creating tables - " . $e->getMessage());
        return false;
    }
}

/**
 * Repair data integrity issues
 */
function dcim_repair_data_integrity() {
    try {
        // Remove orphaned records
        dcim_remove_orphaned_records();
        
        // Fix invalid data
        dcim_fix_invalid_data();
        
        // Update calculated fields
        dcim_update_calculated_fields();
        
        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error repairing data integrity - " . $e->getMessage());
        return false;
    }
}

/**
 * Remove orphaned records that reference non-existent parent records
 */
function dcim_remove_orphaned_records() {
    try {
        // Remove racks with invalid location_id
        if (Capsule::schema()->hasTable('dcim_racks') && Capsule::schema()->hasTable('dcim_locations')) {
            $orphaned_racks = Capsule::table('dcim_racks')
                ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
                ->whereNotNull('dcim_racks.location_id')
                ->whereNull('dcim_locations.id')
                ->pluck('dcim_racks.id');
                
            if (count($orphaned_racks) > 0) {
                Capsule::table('dcim_racks')->whereIn('id', $orphaned_racks)->update(['location_id' => null]);
                error_log("DCIM: Fixed " . count($orphaned_racks) . " orphaned racks");
            }
        }
        
        // Remove servers with invalid rack_id
        if (Capsule::schema()->hasTable('dcim_servers') && Capsule::schema()->hasTable('dcim_racks')) {
            $orphaned_servers = Capsule::table('dcim_servers')
                ->leftJoin('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
                ->whereNotNull('dcim_servers.rack_id')
                ->whereNull('dcim_racks.id')
                ->pluck('dcim_servers.id');
                
            if (count($orphaned_servers) > 0) {
                Capsule::table('dcim_servers')->whereIn('id', $orphaned_servers)->update(['rack_id' => null]);
                error_log("DCIM: Fixed " . count($orphaned_servers) . " orphaned servers");
            }
        }
        
        // Remove IP addresses with invalid subnet_id
        if (Capsule::schema()->hasTable('dcim_ip_addresses') && Capsule::schema()->hasTable('dcim_subnets')) {
            $orphaned_ips = Capsule::table('dcim_ip_addresses')
                ->leftJoin('dcim_subnets', 'dcim_ip_addresses.subnet_id', '=', 'dcim_subnets.id')
                ->whereNull('dcim_subnets.id')
                ->pluck('dcim_ip_addresses.id');
                
            if (count($orphaned_ips) > 0) {
                Capsule::table('dcim_ip_addresses')->whereIn('id', $orphaned_ips)->delete();
                error_log("DCIM: Removed " . count($orphaned_ips) . " orphaned IP addresses");
            }
        }
        
        // Remove IP assignments with invalid ip_address_id
        if (Capsule::schema()->hasTable('dcim_ip_assignments') && Capsule::schema()->hasTable('dcim_ip_addresses')) {
            $orphaned_assignments = Capsule::table('dcim_ip_assignments')
                ->leftJoin('dcim_ip_addresses', 'dcim_ip_assignments.ip_address_id', '=', 'dcim_ip_addresses.id')
                ->whereNull('dcim_ip_addresses.id')
                ->pluck('dcim_ip_assignments.id');
                
            if (count($orphaned_assignments) > 0) {
                Capsule::table('dcim_ip_assignments')->whereIn('id', $orphaned_assignments)->delete();
                error_log("DCIM: Removed " . count($orphaned_assignments) . " orphaned IP assignments");
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error removing orphaned records - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Fix invalid data values
 */
function dcim_fix_invalid_data() {
    try {
        // Fix negative power consumption values
        if (Capsule::schema()->hasTable('dcim_servers')) {
            $fixed_servers = Capsule::table('dcim_servers')
                ->where('power_consumption', '<', 0)
                ->update(['power_consumption' => 0]);
            if ($fixed_servers > 0) {
                error_log("DCIM: Fixed {$fixed_servers} servers with negative power consumption");
            }
        }
        
        if (Capsule::schema()->hasTable('dcim_switches')) {
            $fixed_switches = Capsule::table('dcim_switches')
                ->where('power_consumption', '<', 0)
                ->update(['power_consumption' => 0]);
            if ($fixed_switches > 0) {
                error_log("DCIM: Fixed {$fixed_switches} switches with negative power consumption");
            }
        }
        
        if (Capsule::schema()->hasTable('dcim_chassies')) {
            $fixed_chassis = Capsule::table('dcim_chassies')
                ->where('power_consumption', '<', 0)
                ->update(['power_consumption' => 0]);
            if ($fixed_chassis > 0) {
                error_log("DCIM: Fixed {$fixed_chassis} chassis with negative power consumption");
            }
        }
        
        // Fix invalid rack unit values
        if (Capsule::schema()->hasTable('dcim_servers')) {
            $fixed_units = Capsule::table('dcim_servers')
                ->where('unit_size', '<=', 0)
                ->update(['unit_size' => 1]);
            if ($fixed_units > 0) {
                error_log("DCIM: Fixed {$fixed_units} servers with invalid unit size");
            }
        }
        
        // Fix invalid IP addresses
        if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
            $invalid_ips = Capsule::table('dcim_ip_addresses')
                ->whereRaw('ip_address NOT REGEXP "^([0-9]{1,3}\.){3}[0-9]{1,3}$"')
                ->get();
                
            foreach ($invalid_ips as $ip) {
                if (!filter_var($ip->ip_address, FILTER_VALIDATE_IP)) {
                    Capsule::table('dcim_ip_addresses')
                        ->where('id', $ip->id)
                        ->update(['status' => 'disabled']);
                    error_log("DCIM: Disabled invalid IP address: {$ip->ip_address}");
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error fixing invalid data - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Update calculated fields and statistics
 */
function dcim_update_calculated_fields() {
    try {
        // Update subnet utilization statistics
        if (Capsule::schema()->hasTable('dcim_subnets') && Capsule::schema()->hasTable('dcim_ip_addresses')) {
            $subnets = Capsule::table('dcim_subnets')->get();
            foreach ($subnets as $subnet) {
                $total_ips = pow(2, (32 - $subnet->prefix_length)) - 2; // Exclude network and broadcast
                $assigned_ips = Capsule::table('dcim_ip_addresses')
                    ->where('subnet_id', $subnet->id)
                    ->where('status', 'assigned')
                    ->count();
                    
                // You could add utilization percentage to subnet table if needed
                // For now, just log the statistics
                if ($total_ips > 0) {
                    $utilization = round(($assigned_ips / $total_ips) * 100, 2);
                    error_log("DCIM: Subnet {$subnet->subnet} utilization: {$utilization}% ({$assigned_ips}/{$total_ips})");
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error updating calculated fields - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Create database integrity audit table for tracking issues
 */
function dcim_create_integrity_audit_table() {
    try {
        if (!Capsule::schema()->hasTable('dcim_integrity_audit')) {
            Capsule::schema()->create('dcim_integrity_audit', function ($table) {
                $table->increments('id');
                $table->string('check_type'); // 'orphaned_records', 'invalid_data', 'constraint_violation'
                $table->string('table_name');
                $table->integer('record_id')->nullable();
                $table->string('issue_description');
                $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
                $table->enum('status', ['detected', 'fixed', 'ignored'])->default('detected');
                $table->text('fix_action')->nullable();
                $table->timestamp('detected_at')->useCurrent();
                $table->timestamp('fixed_at')->nullable();
                $table->timestamps();
                
                // Add indexes for performance
                $table->index(['check_type', 'status']);
                $table->index(['table_name', 'record_id']);
                $table->index('detected_at');
            });
            error_log("DCIM: Created integrity audit table");
        }
    } catch (Exception $e) {
        error_log("DCIM: Error creating integrity audit table - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Add enhanced validation constraints
 */
function dcim_add_enhanced_validation_constraints() {
    try {
        // Add check constraints for positive values (MySQL 8.0+ or other databases)
        $constraints = [
            'dcim_servers' => [
                'chk_servers_power_positive' => 'power_consumption >= 0',
                'chk_servers_unit_size_positive' => 'unit_size > 0'
            ],
            'dcim_switches' => [
                'chk_switches_power_positive' => 'power_consumption >= 0',
                'chk_switches_ports_positive' => 'ports > 0'
            ],
            'dcim_chassies' => [
                'chk_chassies_power_positive' => 'power_consumption >= 0',
                'chk_chassies_slots_positive' => 'slots > 0'
            ],
            'dcim_racks' => [
                'chk_racks_units_positive' => 'units > 0',
                'chk_racks_power_positive' => 'power_capacity >= 0'
            ],
            'dcim_locations' => [
                'chk_locations_power_positive' => 'total_power_capacity >= 0'
            ]
        ];
        
        foreach ($constraints as $table => $table_constraints) {
            if (Capsule::schema()->hasTable($table)) {
                foreach ($table_constraints as $constraint_name => $constraint_condition) {
                    try {
                        Capsule::statement("ALTER TABLE {$table} ADD CONSTRAINT {$constraint_name} CHECK ({$constraint_condition})");
                        error_log("DCIM: Added constraint {$constraint_name} to {$table}");
                    } catch (Exception $e) {
                        // Constraint might already exist or database might not support CHECK constraints
                        error_log("DCIM: Could not add constraint {$constraint_name}: " . $e->getMessage());
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error adding validation constraints - " . $e->getMessage());
        // Don't throw - constraints are optional enhancements
    }
}

/**
 * Create automated integrity checking procedures
 */
function dcim_create_integrity_check_procedures() {
    try {
        // Create a comprehensive integrity check function
        // This will be called periodically to detect issues
        error_log("DCIM: Integrity check procedures initialized");
        
        // Run initial integrity check
        $issues = dcim_check_database_integrity();
        if (!empty($issues)) {
            error_log("DCIM: Found " . count($issues) . " integrity issues during migration");
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('migration_check', '', null, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error creating integrity check procedures - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Run comprehensive data validation
 */
function dcim_comprehensive_data_validation() {
    try {
        error_log("DCIM: Starting comprehensive data validation");
        
        // Validate all entity types
        dcim_validate_locations();
        dcim_validate_racks();
        dcim_validate_servers();
        dcim_validate_switches();
        dcim_validate_chassis();
        dcim_validate_subnets();
        dcim_validate_ip_addresses();
        dcim_validate_ip_assignments();
        
        error_log("DCIM: Comprehensive data validation completed");
        
    } catch (Exception $e) {
        error_log("DCIM: Error in comprehensive data validation - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Add performance monitoring indexes
 */
function dcim_add_performance_monitoring_indexes() {
    try {
        // Add composite indexes for common query patterns
        if (Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_rack_status ON dcim_servers(rack_id, status)');
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_servers_client_service ON dcim_servers(client_id, service_id)');
        }
        
        if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_ip_addresses_subnet_status ON dcim_ip_addresses(subnet_id, status)');
        }
        
        if (Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::statement('CREATE INDEX IF NOT EXISTS idx_dcim_subnets_location_type ON dcim_subnets(location_id, subnet_type)');
        }
        
        error_log("DCIM: Added performance monitoring indexes");
        
    } catch (Exception $e) {
        error_log("DCIM: Error adding performance monitoring indexes - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Validate rack data
 */
function dcim_validate_rack($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['name'])) {
        $errors[] = 'Rack name is required';
    }
    
    // Validate location exists
    if (!empty($data['location_id'])) {
        $location_exists = Capsule::table('dcim_locations')
            ->where('id', $data['location_id'])
            ->exists();
        if (!$location_exists) {
            $errors[] = 'Invalid location ID';
        }
    }
    
    // Validate units
    if (isset($data['units']) && (!is_numeric($data['units']) || $data['units'] < 1 || $data['units'] > 50)) {
        $errors[] = 'Units must be between 1 and 50';
    }
    
    // Validate power capacity
    if (isset($data['power_capacity']) && (!is_numeric($data['power_capacity']) || $data['power_capacity'] < 0)) {
        $errors[] = 'Power capacity must be a non-negative number';
    }
    
    return $errors;
}

/**
 * Validate server data
 */
function dcim_validate_server($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['name'])) {
        $errors[] = 'Server name is required';
    }
    
    // Validate rack exists and has space
    if (!empty($data['rack_id'])) {
        $rack = Capsule::table('dcim_racks')->where('id', $data['rack_id'])->first();
        if (!$rack) {
            $errors[] = 'Invalid rack ID';
        } else {
            // Check rack space availability
            if (!empty($data['start_unit']) && !empty($data['unit_size'])) {
                $end_unit = $data['start_unit'] + $data['unit_size'] - 1;
                if ($end_unit > $rack->units) {
                    $errors[] = 'Server exceeds rack capacity';
                }
                
                // Check for conflicts with existing servers
                $conflicts = Capsule::table('dcim_servers')
                    ->where('rack_id', $data['rack_id'])
                    ->where('id', '!=', $data['id'] ?? 0)
                    ->whereNotNull('start_unit')
                    ->get();
                    
                foreach ($conflicts as $server) {
                    $server_end = $server->start_unit + $server->unit_size - 1;
                    if (($data['start_unit'] >= $server->start_unit && $data['start_unit'] <= $server_end) ||
                        ($end_unit >= $server->start_unit && $end_unit <= $server_end)) {
                        $errors[] = 'Rack space conflict with existing server';
                        break;
                    }
                }
            }
        }
    }
    
    // Validate IP address
    if (!empty($data['ip_address']) && !filter_var($data['ip_address'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid IP address format';
    }
    
    // Validate hostname
    if (!empty($data['hostname']) && !preg_match('/^[a-zA-Z0-9.-]+$/', $data['hostname'])) {
        $errors[] = 'Invalid hostname format';
    }
    
    // Validate power consumption
    if (isset($data['power_consumption']) && (!is_numeric($data['power_consumption']) || $data['power_consumption'] < 0)) {
        $errors[] = 'Power consumption must be a non-negative number';
    }
    
    return $errors;
}

/**
 * Validate subnet data
 */
function dcim_validate_subnet($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['subnet'])) {
        $errors[] = 'Subnet CIDR is required';
    } else {
        // Validate CIDR format
        if (!preg_match('/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/', $data['subnet'])) {
            $errors[] = 'Invalid CIDR format';
        } else {
            // Parse and validate network and prefix
            list($network, $prefix) = explode('/', $data['subnet']);
            if (!filter_var($network, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                $errors[] = 'Invalid network address';
            }
            if ($prefix < 1 || $prefix > 32) {
                $errors[] = 'Invalid prefix length (must be 1-32)';
            }
        }
    }
    
    // Check for overlapping subnets
    if (!empty($data['subnet'])) {
        $existing = Capsule::table('dcim_subnets')
            ->where('subnet', $data['subnet'])
            ->where('id', '!=', $data['id'] ?? 0)
            ->exists();
        if ($existing) {
            $errors[] = 'Subnet already exists';
        }
    }
    
    // Validate gateway IP
    if (!empty($data['gateway']) && !filter_var($data['gateway'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid gateway IP address';
    }
    
    // Validate DNS servers
    if (!empty($data['dns_primary']) && !filter_var($data['dns_primary'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid primary DNS server';
    }
    if (!empty($data['dns_secondary']) && !filter_var($data['dns_secondary'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid secondary DNS server';
    }
    
    return $errors;
}

/**
 * Validate IP address data for IPAM operations
 */
function dcim_validate_ip_address_data($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['ip_address'])) {
        $errors[] = 'IP address is required';
    } else {
        // Validate IP format
        if (!filter_var($data['ip_address'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $errors[] = 'Invalid IP address format';
        }
    }
    
    // Validate subnet exists
    if (empty($data['subnet_id'])) {
        $errors[] = 'Subnet ID is required';
    } else {
        $subnet = Capsule::table('dcim_subnets')->where('id', $data['subnet_id'])->first();
        if (!$subnet) {
            $errors[] = 'Invalid subnet ID';
        } else {
            // Check if IP is within subnet range
            if (!empty($data['ip_address'])) {
                $ip_long = ip2long($data['ip_address']);
                $network_long = ip2long($subnet->network);
                $mask = -1 << (32 - $subnet->prefix_length);
                
                if (($ip_long & $mask) !== ($network_long & $mask)) {
                    $errors[] = 'IP address is not within subnet range';
                }
            }
        }
    }
    
    // Check for duplicate IP within subnet
    if (!empty($data['ip_address']) && !empty($data['subnet_id'])) {
        $existing = Capsule::table('dcim_ip_addresses')
            ->where('subnet_id', $data['subnet_id'])
            ->where('ip_address', $data['ip_address'])
            ->where('id', '!=', $data['id'] ?? 0)
            ->exists();
        if ($existing) {
            $errors[] = 'IP address already exists in this subnet';
        }
    }
    
    return $errors;
}

/**
 * Database integrity checking functions
 */

/**
 * Run comprehensive database integrity check
 */
function dcim_check_database_integrity() {
    $issues = [];
    
    try {
        // Check for orphaned records
        $issues = array_merge($issues, dcim_check_orphaned_records());
        
        // Check for invalid data
        $issues = array_merge($issues, dcim_check_invalid_data());
        
        // Check for constraint violations
        $issues = array_merge($issues, dcim_check_constraint_violations());
        
        // Check for missing indexes
        $issues = array_merge($issues, dcim_check_missing_indexes());
        
        return $issues;
    } catch (Exception $e) {
        error_log("DCIM: Error checking database integrity - " . $e->getMessage());
        return ['Error checking database integrity: ' . $e->getMessage()];
    }
}

/**
 * Check for orphaned records
 */
function dcim_check_orphaned_records() {
    $issues = [];
    
    try {
        // Check for racks with invalid location_id
        if (Capsule::schema()->hasTable('dcim_racks') && Capsule::schema()->hasTable('dcim_locations')) {
            $orphaned_count = Capsule::table('dcim_racks')
                ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
                ->whereNotNull('dcim_racks.location_id')
                ->whereNull('dcim_locations.id')
                ->count();
            if ($orphaned_count > 0) {
                $issues[] = "Found {$orphaned_count} racks with invalid location references";
            }
        }
        
        // Check for servers with invalid rack_id
        if (Capsule::schema()->hasTable('dcim_servers') && Capsule::schema()->hasTable('dcim_racks')) {
            $orphaned_count = Capsule::table('dcim_servers')
                ->leftJoin('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
                ->whereNotNull('dcim_servers.rack_id')
                ->whereNull('dcim_racks.id')
                ->count();
            if ($orphaned_count > 0) {
                $issues[] = "Found {$orphaned_count} servers with invalid rack references";
            }
        }
        
        // Check for IP addresses with invalid subnet_id
        if (Capsule::schema()->hasTable('dcim_ip_addresses') && Capsule::schema()->hasTable('dcim_subnets')) {
            $orphaned_count = Capsule::table('dcim_ip_addresses')
                ->leftJoin('dcim_subnets', 'dcim_ip_addresses.subnet_id', '=', 'dcim_subnets.id')
                ->whereNull('dcim_subnets.id')
                ->count();
            if ($orphaned_count > 0) {
                $issues[] = "Found {$orphaned_count} IP addresses with invalid subnet references";
            }
        }
        
    } catch (Exception $e) {
        $issues[] = "Error checking orphaned records: " . $e->getMessage();
    }
    
    return $issues;
}

/**
 * Check for invalid data
 */
function dcim_check_invalid_data() {
    $issues = [];
    
    try {
        // Check for invalid IP addresses
        if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
            $invalid_count = Capsule::table('dcim_ip_addresses')
                ->whereRaw('ip_address NOT REGEXP "^([0-9]{1,3}\.){3}[0-9]{1,3}$"')
                ->count();
            if ($invalid_count > 0) {
                $issues[] = "Found {$invalid_count} invalid IP addresses";
            }
        }
        
        // Check for invalid rack units
        if (Capsule::schema()->hasTable('dcim_servers')) {
            $invalid_count = Capsule::table('dcim_servers')
                ->where(function($query) {
                    $query->where('start_unit', '<', 1)
                          ->orWhere('start_unit', '>', 50);
                })
                ->count();
            if ($invalid_count > 0) {
                $issues[] = "Found {$invalid_count} servers with invalid rack units";
            }
        }
        
        // Check for negative power consumption
        if (Capsule::schema()->hasTable('dcim_servers')) {
            $invalid_count = Capsule::table('dcim_servers')
                ->where('power_consumption', '<', 0)
                ->count();
            if ($invalid_count > 0) {
                $issues[] = "Found {$invalid_count} servers with negative power consumption";
            }
        }
        
    } catch (Exception $e) {
        $issues[] = "Error checking invalid data: " . $e->getMessage();
    }
    
    return $issues;
}

/**
 * Check for constraint violations
 */
function dcim_check_constraint_violations() {
    $issues = [];
    
    try {
        // Check for rack space conflicts
        if (Capsule::schema()->hasTable('dcim_servers')) {
            $servers = Capsule::table('dcim_servers')
                ->whereNotNull('rack_id')
                ->whereNotNull('start_unit')
                ->orderBy('rack_id')
                ->orderBy('start_unit')
                ->get();
                
            $rack_usage = [];
            foreach ($servers as $server) {
                $rack_id = $server->rack_id;
                $start = $server->start_unit;
                $end = $start + $server->unit_size - 1;
                
                if (!isset($rack_usage[$rack_id])) {
                    $rack_usage[$rack_id] = [];
                }
                
                // Check for overlaps
                foreach ($rack_usage[$rack_id] as $used_range) {
                    if (($start >= $used_range[0] && $start <= $used_range[1]) ||
                        ($end >= $used_range[0] && $end <= $used_range[1])) {
                        $issues[] = "Rack space conflict detected for server {$server->name}";
                        break;
                    }
                }
                
                $rack_usage[$rack_id][] = [$start, $end];
            }
        }
        
        // Check for duplicate IP addresses within subnets
        if (Capsule::schema()->hasTable('dcim_ip_addresses')) {
            $duplicates = Capsule::table('dcim_ip_addresses')
                ->select('subnet_id', 'ip_address', Capsule::raw('COUNT(*) as count'))
                ->groupBy('subnet_id', 'ip_address')
                ->having('count', '>', 1)
                ->get();
                
            if (count($duplicates) > 0) {
                $issues[] = "Found " . count($duplicates) . " duplicate IP addresses within subnets";
            }
        }
        
    } catch (Exception $e) {
        $issues[] = "Error checking constraint violations: " . $e->getMessage();
    }
    
    return $issues;
}

/**
 * Check for missing indexes
 */
function dcim_check_missing_indexes() {
    $issues = [];
    
    try {
        // This is database-specific and complex to implement generically
        // For now, we'll just log that indexes should be checked manually
        $issues[] = "Manual index verification recommended - check database performance";
        
    } catch (Exception $e) {
        $issues[] = "Error checking indexes: " . $e->getMessage();
    }
    
    return $issues;
}

/**
 * Add missing foreign keys if tables exist but foreign keys are missing
 */
function dcim_add_missing_foreign_keys() {
    try {
        // Check if racks table has foreign key to locations
        if (Capsule::schema()->hasTable('dcim_racks') && 
            Capsule::schema()->hasTable('dcim_locations')) {
            
            // This is a bit complex to check for existing foreign keys in Laravel
            // For now, we'll rely on the table creation to handle this
        }

        // Check if servers table has foreign key to racks
        if (Capsule::schema()->hasTable('dcim_servers') && 
            Capsule::schema()->hasTable('dcim_racks')) {
            
            // This is a bit complex to check for existing foreign keys in Laravel
            // For now, we'll rely on the table creation to handle this
        }

    } catch (Exception $e) {
        error_log("DCIM: Error adding foreign keys - " . $e->getMessage());
    }
}

/**
 * Populate default CPU models
 */
function dcim_populate_default_cpu_models() {
    try {
        // Check if CPU models already exist
        $existing_count = Capsule::table('dcim_cpu_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $cpu_models = [
            ['name' => 'Intel Xeon E3-1230v6', 'manufacturer' => 'Intel', 'cores' => '4', 'frequency' => '3.5GHz'],
            ['name' => 'Intel Xeon E3-1270v6', 'manufacturer' => 'Intel', 'cores' => '4', 'frequency' => '3.8GHz'],
            ['name' => 'Intel Xeon E5-2620v4', 'manufacturer' => 'Intel', 'cores' => '8', 'frequency' => '2.1GHz'],
            ['name' => 'Intel Xeon E5-2630v4', 'manufacturer' => 'Intel', 'cores' => '10', 'frequency' => '2.2GHz'],
            ['name' => 'Intel Xeon E5-2640v4', 'manufacturer' => 'Intel', 'cores' => '10', 'frequency' => '2.4GHz'],
            ['name' => 'Intel Xeon Gold 5218', 'manufacturer' => 'Intel', 'cores' => '16', 'frequency' => '2.3GHz'],
            ['name' => 'Intel Xeon Gold 6242', 'manufacturer' => 'Intel', 'cores' => '16', 'frequency' => '2.8GHz'],
            ['name' => 'Intel Xeon Platinum 8280', 'manufacturer' => 'Intel', 'cores' => '28', 'frequency' => '2.7GHz'],
            ['name' => 'AMD EPYC 7302', 'manufacturer' => 'AMD', 'cores' => '16', 'frequency' => '3.0GHz'],
            ['name' => 'AMD EPYC 7402', 'manufacturer' => 'AMD', 'cores' => '24', 'frequency' => '2.8GHz'],
            ['name' => 'AMD EPYC 7502', 'manufacturer' => 'AMD', 'cores' => '32', 'frequency' => '2.5GHz'],
        ];

        foreach ($cpu_models as $cpu) {
            $cpu['created_at'] = date('Y-m-d H:i:s');
            $cpu['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_cpu_models')->insert($cpu);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating CPU models - " . $e->getMessage());
    }
}

/**
 * Populate default RAM configurations
 */
function dcim_populate_default_ram_configs() {
    try {
        // Check if RAM configs already exist
        $existing_count = Capsule::table('dcim_ram_configs')->count();
        if ($existing_count > 0) {
            return;
        }

        $ram_configs = [
            ['name' => '16GB DDR4', 'size' => '16GB', 'type' => 'DDR4', 'speed' => '2400MHz'],
            ['name' => '32GB DDR4', 'size' => '32GB', 'type' => 'DDR4', 'speed' => '2400MHz'],
            ['name' => '64GB DDR4', 'size' => '64GB', 'type' => 'DDR4', 'speed' => '2666MHz'],
            ['name' => '128GB DDR4', 'size' => '128GB', 'type' => 'DDR4', 'speed' => '2666MHz'],
            ['name' => '256GB DDR4', 'size' => '256GB', 'type' => 'DDR4', 'speed' => '2933MHz'],
            ['name' => '512GB DDR4', 'size' => '512GB', 'type' => 'DDR4', 'speed' => '2933MHz'],
            ['name' => '1TB DDR4', 'size' => '1TB', 'type' => 'DDR4', 'speed' => '3200MHz'],
        ];

        foreach ($ram_configs as $ram) {
            $ram['created_at'] = date('Y-m-d H:i:s');
            $ram['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_ram_configs')->insert($ram);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating RAM configs - " . $e->getMessage());
    }
}

/**
 * Populate default switch models
 */
function dcim_populate_default_switch_models() {
    try {
        // Check if switch models already exist
        $existing_count = Capsule::table('dcim_switch_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $switch_models = [
            ['name' => 'Cisco Catalyst 2960', 'manufacturer' => 'Cisco', 'model_number' => '2960', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Cisco Catalyst 3560', 'manufacturer' => 'Cisco', 'model_number' => '3560', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Cisco Catalyst 3750', 'manufacturer' => 'Cisco', 'model_number' => '3750', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Cisco Nexus 9300', 'manufacturer' => 'Cisco', 'model_number' => '9300', 'ports' => 48, 'port_speed' => '10G', 'switch_type' => 'core'],
            ['name' => 'HP ProCurve 2920', 'manufacturer' => 'HP', 'model_number' => '2920', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'HP ProCurve 5400', 'manufacturer' => 'HP', 'model_number' => '5400', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Juniper EX2300', 'manufacturer' => 'Juniper', 'model_number' => 'EX2300', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Juniper EX4300', 'manufacturer' => 'Juniper', 'model_number' => 'EX4300', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Arista 7050S', 'manufacturer' => 'Arista', 'model_number' => '7050S', 'ports' => 52, 'port_speed' => '10G', 'switch_type' => 'core'],
            ['name' => 'Dell PowerSwitch N1548', 'manufacturer' => 'Dell', 'model_number' => 'N1548', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'edge'],
        ];

        foreach ($switch_models as $switch) {
            $switch['created_at'] = date('Y-m-d H:i:s');
            $switch['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_switch_models')->insert($switch);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating switch models - " . $e->getMessage());
    }
}

/**
 * Populate default chassis models
 */
function dcim_populate_default_chassis_models() {
    try {
        // Check if chassis models already exist
        $existing_count = Capsule::table('dcim_chassis_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $chassis_models = [
            ['name' => 'Dell PowerEdge M1000e', 'manufacturer' => 'Dell'],
            ['name' => 'HP BladeSystem c7000', 'manufacturer' => 'HP'],
            ['name' => 'IBM BladeCenter H', 'manufacturer' => 'IBM'],
            ['name' => 'Cisco UCS 5108', 'manufacturer' => 'Cisco'],
            ['name' => 'Supermicro SuperBlade', 'manufacturer' => 'Supermicro'],
            ['name' => 'Dell PowerEdge VRTX', 'manufacturer' => 'Dell'],
            ['name' => 'HP ProLiant BL460c', 'manufacturer' => 'HP'],
            ['name' => 'NetApp FAS8200', 'manufacturer' => 'NetApp'],
            ['name' => 'EMC VNX5400', 'manufacturer' => 'EMC'],
            ['name' => 'Hitachi VSP G400', 'manufacturer' => 'Hitachi'],
        ];

        foreach ($chassis_models as $chassis) {
            $chassis['created_at'] = date('Y-m-d H:i:s');
            $chassis['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_chassis_models')->insert($chassis);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating chassis models - " . $e->getMessage());
    }
}

/**
 * Get CPU models from database
 */
function dcim_get_cpu_models() {
    try {
        return Capsule::table('dcim_cpu_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching CPU models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get RAM configurations from database
 */
function dcim_get_ram_configs() {
    try {
        return Capsule::table('dcim_ram_configs')
            ->where('active', true)
            ->orderBy('size')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching RAM configs - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get switch models from database
 */
function dcim_get_switch_models() {
    try {
        return Capsule::table('dcim_switch_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching switch models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get chassis models from database
 */
function dcim_get_chassis_models() {
    try {
        return Capsule::table('dcim_chassis_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching chassis models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Handle AJAX requests for adding CPU models and RAM configs
 */
function dcim_handle_ajax_requests() {
    if ($_POST['ajax_action'] === 'add_cpu_model') {
        try {
            $id = Capsule::table('dcim_cpu_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'cores' => $_POST['cores'] ?? null,
                'frequency' => $_POST['frequency'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_ram_config') {
        try {
            $id = Capsule::table('dcim_ram_configs')->insertGetId([
                'name' => $_POST['name'],
                'size' => $_POST['size'] ?? null,
                'type' => $_POST['type'] ?? null,
                'speed' => $_POST['speed'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_switch_model') {
        try {
            $id = Capsule::table('dcim_switch_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_chassis_model') {
        try {
            $id = Capsule::table('dcim_chassis_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

/**
 * Create sample data for demo purposes
 */
function dcim_create_sample_data() {
    try {
        // Ensure tables exist first
        if (!dcim_ensure_tables_exist()) {
            return false;
        }

        // Check if data already exists
        $existing_locations = Capsule::table('dcim_locations')->count();
        if ($existing_locations > 0) {
            return true; // Data already exists
        }

        // Create sample locations
        $location_data = [
            [
                'name' => 'Primary Data Center',
                'address' => '123 Tech Street, Silicon Valley',
                'city' => 'San Jose',
                'country' => 'United States',
                'contact_name' => 'John Smith',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-0123',
                'total_power_capacity' => 50000,
                'power_unit' => 'Watts',
                'notes' => 'Primary data center facility',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'European Data Center',
                'address' => '456 Server Lane, Frankfurt',
                'city' => 'Frankfurt',
                'country' => 'Germany',
                'contact_name' => 'Anna Mueller',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+49-30-12345678',
                'total_power_capacity' => 30000,
                'power_unit' => 'Watts',
                'notes' => 'European operations center',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($location_data as $location) {
            $location_id = Capsule::table('dcim_locations')->insertGetId($location);

            // Create sample racks for each location
            $rack_data = [
                [
                    'location_id' => $location_id,
                    'name' => 'A01',
                    'row' => 'A',
                    'position' => '01',
                    'units' => 42,
                    'power_capacity' => 5000,
                    'pdu_a' => 'PDU-A01-A',
                    'pdu_b' => 'PDU-A01-B',
                    'notes' => 'Primary server rack',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'location_id' => $location_id,
                    'name' => 'A02',
                    'row' => 'A',
                    'position' => '02',
                    'units' => 42,
                    'power_capacity' => 5000,
                    'pdu_a' => 'PDU-A02-A',
                    'pdu_b' => 'PDU-A02-B',
                    'notes' => 'Secondary server rack',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            foreach ($rack_data as $rack) {
                $rack_id = Capsule::table('dcim_racks')->insertGetId($rack);

                // Create sample servers for the first rack only
                if ($rack['name'] == 'A01') {
                    $server_data = [
                        [
                            'rack_id' => $rack_id,
                            'name' => 'WEB-01',
                            'hostname' => 'web01.example.com',
                            'start_unit' => 40,
                            'unit_size' => 1,
                            'make' => 'Dell',
                            'model' => 'PowerEdge R640',
                            'serial_number' => 'DL001234',
                            'specifications' => '2x Intel Xeon Silver 4214, 64GB RAM, 2x 1TB SSD',
                            'power_consumption' => 300,
                            'ip_address' => '************',
                            'client_id' => null,
                            'service_id' => null,
                            'notes' => 'Web server',
                            'status' => 'online',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ],
                        [
                            'rack_id' => $rack_id,
                            'name' => 'DB-01',
                            'hostname' => 'db01.example.com',
                            'start_unit' => 37,
                            'unit_size' => 2,
                            'make' => 'HPE',
                            'model' => 'ProLiant DL380',
                            'serial_number' => 'HP005678',
                            'specifications' => '2x Intel Xeon Gold 6242, 128GB RAM, 4x 2TB SSD RAID 10',
                            'power_consumption' => 450,
                            'ip_address' => '************',
                            'client_id' => null,
                            'service_id' => null,
                            'notes' => 'Database server',
                            'status' => 'online',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]
                    ];

                    foreach ($server_data as $server) {
                        Capsule::table('dcim_servers')->insert($server);
                    }
                }
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error creating sample data - " . $e->getMessage());
        return false;
    }
}

/**
 * Handle sample data creation request
 */
function dcim_handle_sample_data($modulelink) {
    if ($_GET['create_sample'] === 'true') {
        if (dcim_create_sample_data()) {
            echo '<div class="alert alert-success">Sample data created successfully! <a href="' . $modulelink . '">Refresh page</a></div>';
        } else {
            echo '<div class="alert alert-danger">Error creating sample data. Check error logs for details.</div>';
        }
        return true;
    }
    return false;
}

/**
 * Database maintenance page (embedded version)
 */
function dcim_database_maintenance_page($modulelink) {
    // Add maintenance-specific CSS
    echo '<style>
    .maintenance-content { max-width: 1000px; }
    .status-card { background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0; }
    .status-card h3 { margin-top: 0; color: #2d3748; }
    .action-buttons { margin: 30px 0; }
    .btn-group { display: flex; gap: 10px; flex-wrap: wrap; }
    .btn { display: inline-block; padding: 10px 20px; text-decoration: none; border-radius: 6px; font-weight: 500; border: none; cursor: pointer; }
    .btn-primary { background: #4299e1; color: white; }
    .btn-secondary { background: #718096; color: white; }
    .btn-info { background: #17a2b8; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    .btn-success { background: #28a745; color: white; }
    .btn-danger { background: #dc3545; color: white; }
    .btn:hover { opacity: 0.9; transform: translateY(-1px); }
    .alert { padding: 15px; border-radius: 6px; margin: 15px 0; }
    .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; }
    .table th { background: #f8fafc; font-weight: 600; }
    .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
    .badge-success { background: #d4edda; color: #155724; }
    .badge-danger { background: #f8d7da; color: #721c24; }
    </style>';

    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';

    // Use shared sidebar
    dcim_generate_sidebar($modulelink);

    // Main content area
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="window.location.href=\'' . $modulelink . '\'">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<h1 class="main-title">Database Maintenance</h1>';
    echo '</div>';

    echo '<div style="padding: 24px;">';

    // Handle actions
    $action = $_GET['maintenance_action'] ?? 'status';

    switch ($action) {
        case 'check_integrity':
            dcim_display_integrity_check_embedded($modulelink);
            break;
        case 'repair_data':
            dcim_display_repair_data_embedded($modulelink);
            break;
        case 'run_migration':
            dcim_display_run_migration_embedded($modulelink);
            break;
        case 'optimize_database':
            dcim_display_optimize_database_embedded($modulelink);
            break;
        default:
            dcim_display_status_embedded($modulelink);
            break;
    }

    echo '</div>'; // padding
    echo '</div>'; // dcim-main
    echo '</div>'; // dcim-layout
    echo '</div>'; // dcim-container

    // Include sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Display database status (embedded version)
 */
function dcim_display_status_embedded($modulelink) {
    echo '<div class="maintenance-content">';
    echo '<h2>Database Status</h2>';
    echo '<p>This utility provides comprehensive database maintenance and integrity checking for the DCIM addon.</p>';

    try {
        // Check schema version
        $current_version = dcim_get_schema_version();
        $target_version = DCIM_SCHEMA_VERSION;

        echo '<div class="status-card">';
        echo '<h3>Schema Version</h3>';
        echo '<p><strong>Current:</strong> ' . htmlspecialchars($current_version) . '</p>';
        echo '<p><strong>Target:</strong> ' . htmlspecialchars($target_version) . '</p>';

        if (version_compare($current_version, $target_version, '<')) {
            echo '<div class="alert alert-warning">';
            echo '<strong>⚠️ Migration Required</strong><br>';
            echo 'Your database schema is outdated and needs to be migrated.';
            echo '</div>';
            echo '<p><a href="' . $modulelink . '&action=database_maintenance&maintenance_action=run_migration" class="btn btn-primary">Run Migration</a></p>';
        } else {
            echo '<div class="alert alert-success">';
            echo '<strong>✅ Schema Up to Date</strong><br>';
            echo 'Your database schema is current.';
            echo '</div>';
        }
        echo '</div>';

        // Check table status
        echo '<div class="status-card">';
        echo '<h3>Database Tables</h3>';

        $tables = [
            'dcim_locations' => 'Data center locations',
            'dcim_racks' => 'Server racks',
            'dcim_servers' => 'Physical servers',
            'dcim_switches' => 'Network switches',
            'dcim_chassies' => 'Server chassis',
            'dcim_subnets' => 'IP subnets',
            'dcim_ip_addresses' => 'IP addresses',
            'dcim_ip_assignments' => 'IP assignments'
        ];

        echo '<table class="table table-striped">';
        echo '<thead><tr><th>Table</th><th>Description</th><th>Status</th><th>Records</th></tr></thead>';
        echo '<tbody>';

        foreach ($tables as $table => $description) {
            $exists = Capsule::schema()->hasTable($table);
            $count = $exists ? Capsule::table($table)->count() : 0;
            $status_class = $exists ? 'success' : 'danger';
            $status_text = $exists ? 'EXISTS' : 'MISSING';

            echo '<tr>';
            echo '<td>' . htmlspecialchars($table) . '</td>';
            echo '<td>' . htmlspecialchars($description) . '</td>';
            echo '<td><span class="badge badge-' . $status_class . '">' . $status_text . '</span></td>';
            echo '<td>' . number_format($count) . '</td>';
            echo '</tr>';
        }
        echo '</tbody></table>';
        echo '</div>';

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h3>Error Checking Database Status</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }

    // Action buttons
    echo '<div class="action-buttons">';
    echo '<h3>Maintenance Actions</h3>';
    echo '<div class="btn-group">';
    echo '<a href="' . $modulelink . '&action=database_maintenance&maintenance_action=check_integrity" class="btn btn-info">Check Database Integrity</a>';
    echo '<a href="' . $modulelink . '&action=database_maintenance&maintenance_action=repair_data" class="btn btn-warning">Repair Data Issues</a>';
    echo '<a href="' . $modulelink . '&action=database_maintenance&maintenance_action=optimize_database" class="btn btn-success">Optimize Database</a>';
    echo '</div>';
    echo '</div>';

    echo '</div>'; // maintenance-content
}

/**
 * Display database integrity check (embedded version)
 */
function dcim_display_integrity_check_embedded($modulelink) {
    echo '<div class="maintenance-content">';
    echo '<h2>Database Integrity Check</h2>';
    echo '<p><a href="' . $modulelink . '&action=database_maintenance" class="btn btn-secondary">← Back to Status</a></p>';

    try {
        echo '<div class="alert alert-info">';
        echo '<h3>Running Comprehensive Integrity Check...</h3>';
        echo '</div>';

        $issues = dcim_check_database_integrity();

        if (empty($issues)) {
            echo '<div class="alert alert-success">';
            echo '<h4>✅ No Issues Found</h4>';
            echo '<p>Your database integrity is excellent. No issues were detected.</p>';
            echo '</div>';
        } else {
            echo '<div class="alert alert-warning">';
            echo '<h4>⚠️ Issues Detected</h4>';
            echo '<p>The following issues were found in your database:</p>';
            echo '<ul>';
            foreach ($issues as $issue) {
                echo '<li>' . htmlspecialchars($issue) . '</li>';
            }
            echo '</ul>';
            echo '</div>';
        }

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h3>Error During Integrity Check</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }

    echo '</div>'; // maintenance-content
}

/**
 * Display data repair (embedded version)
 */
function dcim_display_repair_data_embedded($modulelink) {
    echo '<div class="maintenance-content">';
    echo '<h2>Database Data Repair</h2>';
    echo '<p><a href="' . $modulelink . '&action=database_maintenance" class="btn btn-secondary">← Back to Status</a></p>';

    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        try {
            echo '<div class="alert alert-info">';
            echo '<h3>Running Data Repair...</h3>';
            echo '</div>';

            $success = dcim_repair_data_integrity();

            if ($success) {
                echo '<div class="alert alert-success">';
                echo '<h4>✅ Data Repair Completed</h4>';
                echo '<p>Database integrity issues have been repaired successfully.</p>';
                echo '</div>';
            } else {
                echo '<div class="alert alert-danger">';
                echo '<h4>❌ Data Repair Failed</h4>';
                echo '<p>Some issues could not be automatically repaired. Check the server error logs for details.</p>';
                echo '</div>';
            }

        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<h3>Error During Data Repair</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
    } else {
        echo '<div class="alert alert-warning">';
        echo '<h3>⚠️ Confirm Data Repair</h3>';
        echo '<p>This operation will:</p>';
        echo '<ul>';
        echo '<li>Remove orphaned records that reference non-existent parent records</li>';
        echo '<li>Fix invalid data values (negative power consumption, invalid IP addresses, etc.)</li>';
        echo '<li>Update calculated fields and statistics</li>';
        echo '<li>Clean up inconsistent data</li>';
        echo '</ul>';
        echo '<p><strong>Note:</strong> This operation is generally safe but will modify your database.</p>';
        echo '<p><a href="' . $modulelink . '&action=database_maintenance&maintenance_action=repair_data&confirm=yes" class="btn btn-warning">Yes, Repair Data</a></p>';
        echo '</div>';
    }

    echo '</div>'; // maintenance-content
}

/**
 * Display migration (embedded version)
 */
function dcim_display_run_migration_embedded($modulelink) {
    echo '<div class="maintenance-content">';
    echo '<h2>Database Schema Migration</h2>';
    echo '<p><a href="' . $modulelink . '&action=database_maintenance" class="btn btn-secondary">← Back to Status</a></p>';

    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        try {
            echo '<div class="alert alert-info">';
            echo '<h3>Running Database Migration...</h3>';
            echo '</div>';

            $success = dcim_run_migrations();

            if ($success) {
                echo '<div class="alert alert-success">';
                echo '<h4>✅ Migration Completed Successfully</h4>';
                echo '<p>Your database schema has been updated to version ' . htmlspecialchars(DCIM_SCHEMA_VERSION) . '.</p>';
                echo '</div>';
            } else {
                echo '<div class="alert alert-danger">';
                echo '<h4>❌ Migration Failed</h4>';
                echo '<p>The database migration could not be completed. Check the server error logs for details.</p>';
                echo '</div>';
            }

        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<h3>Error During Migration</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
    } else {
        $current_version = dcim_get_schema_version();
        $target_version = DCIM_SCHEMA_VERSION;

        echo '<div class="alert alert-info">';
        echo '<h3>📋 Migration Information</h3>';
        echo '<p><strong>Current Schema Version:</strong> ' . htmlspecialchars($current_version) . '</p>';
        echo '<p><strong>Target Schema Version:</strong> ' . htmlspecialchars($target_version) . '</p>';

        if (version_compare($current_version, $target_version, '>=')) {
            echo '<div class="alert alert-success">';
            echo '<p>✅ Your database is already up to date. No migration is needed.</p>';
            echo '</div>';
        } else {
            echo '<p>This migration will update your database schema with improvements.</p>';
            echo '<p><a href="' . $modulelink . '&action=database_maintenance&maintenance_action=run_migration&confirm=yes" class="btn btn-primary">Run Migration</a></p>';
        }
        echo '</div>';
    }

    echo '</div>'; // maintenance-content
}

/**
 * Display database optimization (embedded version)
 */
function dcim_display_optimize_database_embedded($modulelink) {
    echo '<div class="maintenance-content">';
    echo '<h2>Database Optimization</h2>';
    echo '<p><a href="' . $modulelink . '&action=database_maintenance" class="btn btn-secondary">← Back to Status</a></p>';

    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        try {
            echo '<div class="alert alert-info">';
            echo '<h3>Running Database Optimization...</h3>';
            echo '</div>';

            // Add indexes if not already present
            dcim_add_database_indexes();

            echo '<div class="alert alert-success">';
            echo '<h4>✅ Database Optimization Completed</h4>';
            echo '<p>Database indexes have been optimized for better performance.</p>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<h3>Error During Optimization</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
    } else {
        echo '<div class="alert alert-success">';
        echo '<h3>🚀 Database Optimization</h3>';
        echo '<p>This operation will optimize your DCIM database for better performance by:</p>';
        echo '<ul>';
        echo '<li>Adding database indexes for faster queries</li>';
        echo '<li>Optimizing table storage and fragmentation</li>';
        echo '<li>Improving query performance for large datasets</li>';
        echo '</ul>';
        echo '<p><strong>Note:</strong> This operation is safe and will not modify your data, only improve performance.</p>';
        echo '<p><a href="' . $modulelink . '&action=database_maintenance&maintenance_action=optimize_database&confirm=yes" class="btn btn-success">Optimize Database</a></p>';
        echo '</div>';
    }

    echo '</div>'; // maintenance-content
}

/**
 * Get country flag emoji based on country name
 */
function dcim_get_country_flag($country) {
    if (empty($country)) {
        return '🌍'; // Default globe icon
    }
    
    $country = strtolower(trim($country));
    
    $country_flags = [
        'united states' => '🇺🇸',
        'usa' => '🇺🇸',
        'us' => '🇺🇸',
        'germany' => '🇩🇪',
        'united kingdom' => '🇬🇧',
        'uk' => '🇬🇧',
        'france' => '🇫🇷',
        'canada' => '🇨🇦',
        'australia' => '🇦🇺',
        'japan' => '🇯🇵',
        'china' => '🇨🇳',
        'india' => '🇮🇳',
        'brazil' => '🇧🇷',
        'russia' => '🇷🇺',
        'south korea' => '🇰🇷',
        'italy' => '🇮🇹',
        'spain' => '🇪🇸',
        'netherlands' => '🇳🇱',
        'sweden' => '🇸🇪',
        'norway' => '🇳🇴',
        'denmark' => '🇩🇰',
        'finland' => '🇫🇮',
        'switzerland' => '🇨🇭',
        'austria' => '🇦🇹',
        'belgium' => '🇧🇪',
        'ireland' => '🇮🇪',
        'portugal' => '🇵🇹',
        'poland' => '🇵🇱',
        'czech republic' => '🇨🇿',
        'hungary' => '🇭🇺',
        'greece' => '🇬🇷',
        'turkey' => '🇹🇷',
        'israel' => '🇮🇱',
        'south africa' => '🇿🇦',
        'mexico' => '🇲🇽',
        'argentina' => '🇦🇷',
        'chile' => '🇨🇱',
        'colombia' => '🇨🇴',
        'peru' => '🇵🇪',
        'venezuela' => '🇻🇪',
        'thailand' => '🇹🇭',
        'vietnam' => '🇻🇳',
        'singapore' => '🇸🇬',
        'malaysia' => '🇲🇾',
        'indonesia' => '🇮🇩',
        'philippines' => '🇵🇭',
        'new zealand' => '🇳🇿',
        'ukraine' => '🇺🇦',
        'romania' => '🇷🇴',
        'bulgaria' => '🇧🇬',
        'croatia' => '🇭🇷',
        'slovenia' => '🇸🇮',
        'slovakia' => '🇸🇰',
        'lithuania' => '🇱🇹',
        'latvia' => '🇱🇻',
        'estonia' => '🇪🇪',
        'serbia' => '🇷🇸',
        'bosnia and herzegovina' => '🇧🇦',
        'montenegro' => '🇲🇪',
        'north macedonia' => '🇲🇰',
        'albania' => '🇦🇱',
        'luxembourg' => '🇱🇺',
        'iceland' => '🇮🇸',
        'malta' => '🇲🇹',
        'cyprus' => '🇨🇾',
        'monaco' => '🇲🇨',
        'liechtenstein' => '🇱🇮',
        'andorra' => '🇦🇩',
        'san marino' => '🇸🇲',
        'vatican city' => '🇻🇦',
        'egypt' => '🇪🇬',
        'morocco' => '🇲🇦',
        'tunisia' => '🇹🇳',
        'algeria' => '🇩🇿',
        'libya' => '🇱🇾',
        'nigeria' => '🇳🇬',
        'kenya' => '🇰🇪',
        'ghana' => '🇬🇭',
        'ethiopia' => '🇪🇹',
        'uganda' => '🇺🇬',
        'tanzania' => '🇹🇿',
        'cameroon' => '🇨🇲',
        'ivory coast' => '🇨🇮',
        'senegal' => '🇸🇳',
        'madagascar' => '🇲🇬',
        'botswana' => '🇧🇼',
        'namibia' => '🇳🇦',
        'zambia' => '🇿🇲',
        'zimbabwe' => '🇿🇼',
        'mozambique' => '🇲🇿',
        'malawi' => '🇲🇼',
        'rwanda' => '🇷🇼',
        'burundi' => '🇧🇮',
        'djibouti' => '🇩🇯',
        'eritrea' => '🇪🇷',
        'somalia' => '🇸🇴',
        'sudan' => '🇸🇩',
        'south sudan' => '🇸🇸',
        'chad' => '🇹🇩',
        'central african republic' => '🇨🇫',
        'democratic republic of the congo' => '🇨🇩',
        'republic of the congo' => '🇨🇬',
        'gabon' => '🇬🇦',
        'equatorial guinea' => '🇬🇶',
        'sao tome and principe' => '🇸🇹',
        'cape verde' => '🇨🇻',
        'guinea' => '🇬🇳',
        'guinea-bissau' => '🇬🇼',
        'sierra leone' => '🇸🇱',
        'liberia' => '🇱🇷',
        'burkina faso' => '🇧🇫',
        'mali' => '🇲🇱',
        'niger' => '🇳🇪',
        'mauritania' => '🇲🇷',
        'the gambia' => '🇬🇲',
        'gambia' => '🇬🇲',
        'mauritius' => '🇲🇺',
        'seychelles' => '🇸🇨',
        'comoros' => '🇰🇲',
        'lesotho' => '🇱🇸',
        'eswatini' => '🇸🇿',
        'swaziland' => '🇸🇿'
    ];
    
    return $country_flags[$country] ?? '🌍';
}

/**
 * IPAM Utility Functions
 */

/**
 * Validate IP address format
 */
function dcim_validate_ip($ip, $version = 'IPv4') {
    if ($version === 'IPv4') {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
    } else {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false;
    }
}

/**
 * Validate CIDR notation
 */
function dcim_validate_cidr($cidr, $version = 'IPv4') {
    if (!preg_match('/^(.+)\/(\d+)$/', $cidr, $matches)) {
        return false;
    }

    $ip = $matches[1];
    $prefix = (int)$matches[2];

    if (!dcim_validate_ip($ip, $version)) {
        return false;
    }

    if ($version === 'IPv4') {
        return $prefix >= 0 && $prefix <= 32;
    } else {
        return $prefix >= 0 && $prefix <= 128;
    }
}

/**
 * Calculate network address from CIDR
 */
function dcim_calculate_network($cidr) {
    if (!preg_match('/^(.+)\/(\d+)$/', $cidr, $matches)) {
        return false;
    }

    $ip = $matches[1];
    $prefix = (int)$matches[2];

    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $ip_long = ip2long($ip);
        $mask = -1 << (32 - $prefix);
        $network_long = $ip_long & $mask;
        return long2ip($network_long);
    }

    // IPv6 support would go here
    return false;
}

/**
 * Calculate total IPs in subnet
 */
function dcim_calculate_total_ips($prefix_length, $version = 'IPv4') {
    if ($version === 'IPv4') {
        return pow(2, 32 - $prefix_length);
    } else {
        return pow(2, 128 - $prefix_length);
    }
}

/**
 * Generate IP addresses for a subnet
 */
function dcim_generate_subnet_ips($subnet_id, $network, $prefix_length) {
    try {
        $ip_long = ip2long($network);
        $total_ips = pow(2, 32 - $prefix_length);

        // Don't generate more than 1000 IPs at once for performance
        $max_ips = min($total_ips, 1000);

        $ips = [];
        for ($i = 0; $i < $max_ips; $i++) {
            $ip = long2ip($ip_long + $i);
            $status = 'available';

            // First IP is usually network address
            if ($i === 0) {
                $status = 'reserved';
            }
            // Last IP is usually broadcast address
            if ($i === $total_ips - 1 && $prefix_length < 31) {
                $status = 'reserved';
            }

            $ips[] = [
                'subnet_id' => $subnet_id,
                'ip_address' => $ip,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        // Batch insert for performance
        if (!empty($ips)) {
            Capsule::table('dcim_ip_addresses')->insert($ips);
        }

        return count($ips);
    } catch (Exception $e) {
        error_log("DCIM: Error generating subnet IPs - " . $e->getMessage());
        return false;
    }
}

/**
 * Create a new subnet
 */
function dcim_create_subnet($data) {
    try {
        // Validate CIDR
        if (!dcim_validate_cidr($data['subnet'], $data['ip_version'])) {
            return ['success' => false, 'error' => 'Invalid CIDR notation'];
        }

        // Extract network and prefix
        list($network, $prefix_length) = explode('/', $data['subnet']);
        $network = dcim_calculate_network($data['subnet']);

        // Check if subnet already exists
        $existing = Capsule::table('dcim_subnets')
            ->where('subnet', $data['subnet'])
            ->first();

        if ($existing) {
            return ['success' => false, 'error' => 'Subnet already exists'];
        }

        // Create subnet
        $subnet_id = Capsule::table('dcim_subnets')->insertGetId([
            'subnet' => $data['subnet'],
            'network' => $network,
            'prefix_length' => $prefix_length,
            'ip_version' => $data['ip_version'],
            'location_id' => !empty($data['location_id']) ? $data['location_id'] : null,
            'country' => $data['country'] ?? null,
            'city' => $data['city'] ?? null,
            'is_public' => $data['is_public'] ?? false,
            'subnet_type' => $data['subnet_type'] ?? 'Root',
            'note' => $data['note'] ?? null,
            'status' => 'Available',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // Generate IP addresses for the subnet
        if ($data['ip_version'] === 'IPv4') {
            dcim_generate_subnet_ips($subnet_id, $network, $prefix_length);
        }

        return ['success' => true, 'subnet_id' => $subnet_id];
    } catch (Exception $e) {
        error_log("DCIM: Error creating subnet - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Delete a subnet and all its IP addresses
 */
function dcim_delete_subnet($subnet_id) {
    try {
        // Delete all IP addresses first (cascade should handle this, but being explicit)
        Capsule::table('dcim_ip_addresses')->where('subnet_id', $subnet_id)->delete();

        // Delete the subnet
        $deleted = Capsule::table('dcim_subnets')->where('id', $subnet_id)->delete();

        return ['success' => true, 'deleted' => $deleted > 0];
    } catch (Exception $e) {
        error_log("DCIM: Error deleting subnet - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Log integrity audit issue
 */
function dcim_log_integrity_issue($check_type, $table_name, $record_id, $description, $severity = 'medium') {
    try {
        if (Capsule::schema()->hasTable('dcim_integrity_audit')) {
            Capsule::table('dcim_integrity_audit')->insert([
                'check_type' => $check_type,
                'table_name' => $table_name,
                'record_id' => $record_id,
                'issue_description' => $description,
                'severity' => $severity,
                'status' => 'detected',
                'detected_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error logging integrity issue - " . $e->getMessage());
    }
}











/**
 * Validate locations data
 */
function dcim_validate_locations() {
    try {
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            return;
        }
        
        $locations = Capsule::table('dcim_locations')->get();
        foreach ($locations as $location) {
            $issues = [];
            
            // Validate email format
            if (!empty($location->contact_email) && !filter_var($location->contact_email, FILTER_VALIDATE_EMAIL)) {
                $issues[] = "Invalid email format: {$location->contact_email}";
            }
            
            // Validate power capacity
            if ($location->total_power_capacity < 0) {
                $issues[] = "Negative power capacity: {$location->total_power_capacity}";
                Capsule::table('dcim_locations')
                    ->where('id', $location->id)
                    ->update(['total_power_capacity' => 0]);
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_locations', $location->id, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating locations - " . $e->getMessage());
    }
}

/**
 * Validate racks data
 */
function dcim_validate_racks() {
    try {
        if (!Capsule::schema()->hasTable('dcim_racks')) {
            return;
        }
        
        $racks = Capsule::table('dcim_racks')->get();
        foreach ($racks as $rack) {
            $issues = [];
            
            // Validate unit count
            if ($rack->units <= 0) {
                $issues[] = "Invalid unit count: {$rack->units}";
                Capsule::table('dcim_racks')
                    ->where('id', $rack->id)
                    ->update(['units' => 42]); // Default to 42U
            }
            
            // Validate power capacity
            if ($rack->power_capacity < 0) {
                $issues[] = "Negative power capacity: {$rack->power_capacity}";
                Capsule::table('dcim_racks')
                    ->where('id', $rack->id)
                    ->update(['power_capacity' => 0]);
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_racks', $rack->id, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating racks - " . $e->getMessage());
    }
}

/**
 * Validate servers data
 */
function dcim_validate_servers() {
    try {
        if (!Capsule::schema()->hasTable('dcim_servers')) {
            return;
        }
        
        $servers = Capsule::table('dcim_servers')->get();
        foreach ($servers as $server) {
            $issues = [];
            
            // Validate unit size
            if ($server->unit_size <= 0) {
                $issues[] = "Invalid unit size: {$server->unit_size}";
                Capsule::table('dcim_servers')
                    ->where('id', $server->id)
                    ->update(['unit_size' => 1]);
            }
            
            // Validate power consumption
            if ($server->power_consumption < 0) {
                $issues[] = "Negative power consumption: {$server->power_consumption}";
                Capsule::table('dcim_servers')
                    ->where('id', $server->id)
                    ->update(['power_consumption' => 0]);
            }
            
            // Validate IP address format
            if (!empty($server->ip_address) && !filter_var($server->ip_address, FILTER_VALIDATE_IP)) {
                $issues[] = "Invalid IP address format: {$server->ip_address}";
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_servers', $server->id, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating servers - " . $e->getMessage());
    }
}

/**
 * Validate switches data
 */
function dcim_validate_switches() {
    try {
        if (!Capsule::schema()->hasTable('dcim_switches')) {
            return;
        }
        
        $switches = Capsule::table('dcim_switches')->get();
        foreach ($switches as $switch) {
            $issues = [];
            
            // Validate port count
            if ($switch->ports <= 0) {
                $issues[] = "Invalid port count: {$switch->ports}";
                Capsule::table('dcim_switches')
                    ->where('id', $switch->id)
                    ->update(['ports' => 24]); // Default to 24 ports
            }
            
            // Validate power consumption
            if ($switch->power_consumption < 0) {
                $issues[] = "Negative power consumption: {$switch->power_consumption}";
                Capsule::table('dcim_switches')
                    ->where('id', $switch->id)
                    ->update(['power_consumption' => 0]);
            }
            
            // Validate management IP
            if (!empty($switch->management_ip) && !filter_var($switch->management_ip, FILTER_VALIDATE_IP)) {
                $issues[] = "Invalid management IP format: {$switch->management_ip}";
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_switches', $switch->id, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating switches - " . $e->getMessage());
    }
}

/**
 * Validate chassis data
 */
function dcim_validate_chassis() {
    try {
        if (!Capsule::schema()->hasTable('dcim_chassies')) {
            return;
        }
        
        $chassis = Capsule::table('dcim_chassies')->get();
        foreach ($chassis as $chassi) {
            $issues = [];
            
            // Validate slot count
            if ($chassi->slots <= 0) {
                $issues[] = "Invalid slot count: {$chassi->slots}";
                Capsule::table('dcim_chassies')
                    ->where('id', $chassi->id)
                    ->update(['slots' => 8]); // Default to 8 slots
            }
            
            // Validate power consumption
            if ($chassi->power_consumption < 0) {
                $issues[] = "Negative power consumption: {$chassi->power_consumption}";
                Capsule::table('dcim_chassies')
                    ->where('id', $chassi->id)
                    ->update(['power_consumption' => 0]);
            }
            
            // Validate management IP
            if (!empty($chassi->management_ip) && !filter_var($chassi->management_ip, FILTER_VALIDATE_IP)) {
                $issues[] = "Invalid management IP format: {$chassi->management_ip}";
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_chassies', $chassi->id, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating chassis - " . $e->getMessage());
    }
}

/**
 * Validate subnets data
 */
function dcim_validate_subnets() {
    try {
        if (!Capsule::schema()->hasTable('dcim_subnets')) {
            return;
        }
        
        $subnets = Capsule::table('dcim_subnets')->get();
        foreach ($subnets as $subnet) {
            $issues = [];
            
            // Validate CIDR notation
            if (!dcim_validate_cidr($subnet->subnet)) {
                $issues[] = "Invalid CIDR notation: {$subnet->subnet}";
            }
            
            // Validate prefix length
            if ($subnet->prefix_length < 0 || $subnet->prefix_length > 32) {
                $issues[] = "Invalid prefix length: {$subnet->prefix_length}";
            }
            
            // Validate network address
            if (!filter_var($subnet->network, FILTER_VALIDATE_IP)) {
                $issues[] = "Invalid network address: {$subnet->network}";
            }
            
            // Validate gateway if provided
            if (!empty($subnet->gateway) && !filter_var($subnet->gateway, FILTER_VALIDATE_IP)) {
                $issues[] = "Invalid gateway address: {$subnet->gateway}";
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_subnets', $subnet->id, $issue, 'high');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating subnets - " . $e->getMessage());
    }
}

/**
 * Validate IP addresses data
 */
function dcim_validate_ip_addresses() {
    try {
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            return;
        }
        
        $ip_addresses = Capsule::table('dcim_ip_addresses')->get();
        foreach ($ip_addresses as $ip) {
            $issues = [];
            
            // Validate IP address format
            if (!filter_var($ip->ip_address, FILTER_VALIDATE_IP)) {
                $issues[] = "Invalid IP address format: {$ip->ip_address}";
                // Disable invalid IP addresses
                Capsule::table('dcim_ip_addresses')
                    ->where('id', $ip->id)
                    ->update(['status' => 'disabled']);
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_ip_addresses', $ip->id, $issue, 'high');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating IP addresses - " . $e->getMessage());
    }
}

/**
 * Validate IP assignments data
 */
function dcim_validate_ip_assignments() {
    try {
        if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
            return;
        }
        
        $assignments = Capsule::table('dcim_ip_assignments')->get();
        foreach ($assignments as $assignment) {
            $issues = [];
            
            // Validate device exists
            $device_exists = false;
            switch ($assignment->device_type) {
                case 'server':
                    if (Capsule::schema()->hasTable('dcim_servers')) {
                        $device_exists = Capsule::table('dcim_servers')->where('id', $assignment->device_id)->exists();
                    }
                    break;
                case 'switch':
                    if (Capsule::schema()->hasTable('dcim_switches')) {
                        $device_exists = Capsule::table('dcim_switches')->where('id', $assignment->device_id)->exists();
                    }
                    break;
                case 'chassis':
                    if (Capsule::schema()->hasTable('dcim_chassies')) {
                        $device_exists = Capsule::table('dcim_chassies')->where('id', $assignment->device_id)->exists();
                    }
                    break;
            }
            
            if (!$device_exists) {
                $issues[] = "Device {$assignment->device_type} ID {$assignment->device_id} does not exist";
                // Remove invalid assignment
                Capsule::table('dcim_ip_assignments')->where('id', $assignment->id)->delete();
            }
            
            // Log issues
            foreach ($issues as $issue) {
                dcim_log_integrity_issue('validation', 'dcim_ip_assignments', $assignment->id, $issue, 'medium');
            }
        }
        
    } catch (Exception $e) {
        error_log("DCIM: Error validating IP assignments - " . $e->getMessage());
    }
}

/**
 * Get comprehensive validation report
 */
function dcim_get_validation_report() {
    try {
        $report = [
            'summary' => [
                'total_issues' => 0,
                'critical_issues' => 0,
                'high_issues' => 0,
                'medium_issues' => 0,
                'low_issues' => 0
            ],
            'issues_by_table' => [],
            'recent_issues' => []
        ];
        
        if (Capsule::schema()->hasTable('dcim_integrity_audit')) {
            // Get summary counts
            $summary = Capsule::table('dcim_integrity_audit')
                ->select('severity', Capsule::raw('COUNT(*) as count'))
                ->where('status', 'detected')
                ->groupBy('severity')
                ->get();
                
            foreach ($summary as $item) {
                $report['summary'][$item->severity . '_issues'] = $item->count;
                $report['summary']['total_issues'] += $item->count;
            }
            
            // Get issues by table
            $by_table = Capsule::table('dcim_integrity_audit')
                ->select('table_name', Capsule::raw('COUNT(*) as count'))
                ->where('status', 'detected')
                ->groupBy('table_name')
                ->get();
                
            foreach ($by_table as $item) {
                $report['issues_by_table'][$item->table_name] = $item->count;
            }
            
            // Get recent issues
            $report['recent_issues'] = Capsule::table('dcim_integrity_audit')
                ->where('status', 'detected')
                ->orderBy('detected_at', 'desc')
                ->limit(10)
                ->get()
                ->toArray();
        }
        
        return $report;
        
    } catch (Exception $e) {
        error_log("DCIM: Error generating validation report - " . $e->getMessage());
        return null;
    }
}