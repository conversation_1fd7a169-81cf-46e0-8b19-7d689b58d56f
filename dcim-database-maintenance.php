<?php
/**
 * DCIM Database Maintenance and Integrity Checker
 *
 * This script provides database maintenance utilities including:
 * - Schema version checking and migration
 * - Database integrity checking
 * - Data repair and cleanup
 * - Performance optimization
 *
 * Usage: Access this file directly via browser: /modules/addons/dcim/dcim-database-maintenance.php
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Basic connectivity test
echo '<!-- DCIM Database Maintenance Script Loading... -->';
flush();

if (!defined("WHMCS")) {
    // Allow direct access for debugging
    $init_paths = [
        '../../../init.php',
        '../../init.php',
        '../init.php',
        'init.php',
        '/whmcs/init.php',
        dirname(__FILE__) . '/../../../init.php'
    ];

    $init_loaded = false;
    $tried_paths = [];

    foreach ($init_paths as $path) {
        $tried_paths[] = realpath($path) ?: $path;
        if (file_exists($path)) {
            try {
                require_once $path;
                $init_loaded = true;
                break;
            } catch (Exception $e) {
                echo "Error loading $path: " . $e->getMessage() . "<br>";
                continue;
            }
        }
    }

    if (!$init_loaded) {
        echo '<h3>WHMCS Initialization Debug Information</h3>';
        echo '<p><strong>Current directory:</strong> ' . __DIR__ . '</p>';
        echo '<p><strong>Script path:</strong> ' . __FILE__ . '</p>';
        echo '<p><strong>Tried paths:</strong></p><ul>';
        foreach ($tried_paths as $path) {
            $exists = file_exists($path) ? 'EXISTS' : 'NOT FOUND';
            echo '<li>' . htmlspecialchars($path) . ' - ' . $exists . '</li>';
        }
        echo '</ul>';
        die('<p><strong>Error:</strong> Could not locate WHMCS init.php file. Please ensure this script is in the correct location.</p>');
    }
}

use WHMCS\Database\Capsule;

// Include core functions with error handling
try {
    if (!file_exists(__DIR__ . '/dcim-core.php')) {
        die('Error: dcim-core.php not found in ' . __DIR__);
    }
    require_once __DIR__ . '/dcim-core.php';
} catch (Exception $e) {
    die('Error loading dcim-core.php: ' . $e->getMessage());
} catch (Error $e) {
    die('Fatal error loading dcim-core.php: ' . $e->getMessage());
}

// Verify WHMCS is properly loaded
if (!defined('WHMCS') || !class_exists('WHMCS\Database\Capsule')) {
    die('Error: WHMCS not properly initialized. Please ensure this script is accessed through WHMCS.');
}

// Add basic styling
echo '<style>
body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 20px; background: #f8fafc; }
.container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
h1 { color: #2d3748; border-bottom: 2px solid #e2e8f0; padding-bottom: 10px; }
h2 { color: #4a5568; margin-top: 30px; }
h3 { color: #2d3748; }
.back-btn { display: inline-block; background: #4299e1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 6px; margin-bottom: 20px; }
.back-btn:hover { background: #3182ce; }
</style>';

echo '<div class="container">';
echo '<a href="javascript:history.back()" class="back-btn">← Back to DCIM</a>';
echo '<h1>DCIM Database Maintenance</h1>';
echo '<p>This utility provides comprehensive database maintenance and integrity checking for the DCIM addon.</p>';

// Handle actions
$action = $_GET['action'] ?? 'status';

switch ($action) {
    case 'check_integrity':
        dcim_display_integrity_check();
        break;
    case 'repair_data':
        dcim_display_repair_data();
        break;
    case 'run_migration':
        dcim_display_run_migration();
        break;
    case 'optimize_database':
        dcim_display_optimize_database();
        break;
    default:
        dcim_display_status();
        break;
}

/**
 * Display current database status
 */
function dcim_display_status() {
    echo '<h2>Database Status</h2>';
    
    try {
        // Check schema version
        $current_version = dcim_get_schema_version();
        $target_version = DCIM_SCHEMA_VERSION;
        
        echo '<div class="status-panel">';
        echo '<h3>Schema Version</h3>';
        echo '<p><strong>Current Version:</strong> ' . htmlspecialchars($current_version) . '</p>';
        echo '<p><strong>Target Version:</strong> ' . htmlspecialchars($target_version) . '</p>';
        
        if (version_compare($current_version, $target_version, '<')) {
            echo '<div style="color: orange; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;">';
            echo '<strong>⚠️ Migration Required</strong><br>';
            echo 'Your database schema is outdated and needs to be migrated.';
            echo '</div>';
            echo '<p><a href="?action=run_migration" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Run Migration</a></p>';
        } else {
            echo '<div style="color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;">';
            echo '<strong>✅ Schema Up to Date</strong><br>';
            echo 'Your database schema is current.';
            echo '</div>';
        }
        echo '</div>';
        
        // Check table status
        echo '<div class="status-panel">';
        echo '<h3>Table Status</h3>';
        $tables = [
            'dcim_schema_version' => 'Schema Version Tracking',
            'dcim_integrity_audit' => 'Database Integrity Audit Log',
            'dcim_locations' => 'Data Center Locations',
            'dcim_racks' => 'Equipment Racks',
            'dcim_servers' => 'Server Inventory',
            'dcim_switches' => 'Network Switches',
            'dcim_chassies' => 'Chassis Systems',
            'dcim_subnets' => 'IP Subnets',
            'dcim_ip_addresses' => 'IP Address Pool',
            'dcim_ip_assignments' => 'IP Device Assignments',
            'dcim_cpu_models' => 'CPU Model Reference',
            'dcim_ram_configs' => 'RAM Configuration Reference',
            'dcim_switch_models' => 'Switch Model Reference',
            'dcim_chassis_models' => 'Chassis Model Reference'
        ];
        
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>Table</th><th>Description</th><th>Status</th><th>Records</th></tr>';
        
        foreach ($tables as $table => $description) {
            $exists = Capsule::schema()->hasTable($table);
            $count = $exists ? Capsule::table($table)->count() : 0;
            $status_color = $exists ? 'green' : 'red';
            $status_text = $exists ? 'EXISTS' : 'MISSING';
            
            echo '<tr>';
            echo '<td>' . htmlspecialchars($table) . '</td>';
            echo '<td>' . htmlspecialchars($description) . '</td>';
            echo '<td style="color: ' . $status_color . '; font-weight: bold;">' . $status_text . '</td>';
            echo '<td>' . number_format($count) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
        echo '<h3>Error</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    
    // Action buttons
    echo '<div class="action-buttons" style="margin: 20px 0;">';
    echo '<h3>Maintenance Actions</h3>';
    echo '<p><a href="?action=check_integrity" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Check Database Integrity</a></p>';
    echo '<p><a href="?action=repair_data" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Repair Data Issues</a></p>';
    echo '<p><a href="?action=optimize_database" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Optimize Database</a></p>';
    echo '</div>';
}

/**
 * Display database integrity check results
 */
function dcim_display_integrity_check() {
    echo '<h2>Database Integrity Check</h2>';
    echo '<p><a href="?">← Back to Status</a></p>';
    
    try {
        echo '<div style="background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;">';
        echo '<h3>Running Comprehensive Integrity Check...</h3>';
        
        $issues = dcim_check_database_integrity();
        
        if (empty($issues)) {
            echo '<div style="color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;">';
            echo '<h4>✅ No Issues Found</h4>';
            echo '<p>Your database integrity is excellent. No issues were detected.</p>';
            echo '</div>';
        } else {
            echo '<div style="color: orange; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;">';
            echo '<h4>⚠️ Issues Detected</h4>';
            echo '<p>The following issues were found in your database:</p>';
            echo '<ul>';
            foreach ($issues as $issue) {
                echo '<li>' . htmlspecialchars($issue) . '</li>';
            }
            echo '</ul>';
            echo '<p><a href="?action=repair_data" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Repair These Issues</a></p>';
            echo '</div>';
        }
        
        // Display validation report if available
        $report = dcim_get_validation_report();
        if ($report) {
            echo '<div style="background: #e9ecef; padding: 15px; border: 1px solid #ced4da; margin: 10px 0;">';
            echo '<h4>📊 Validation Report Summary</h4>';
            echo '<p><strong>Total Issues:</strong> ' . $report['summary']['total_issues'] . '</p>';
            if ($report['summary']['total_issues'] > 0) {
                echo '<ul>';
                if ($report['summary']['critical_issues'] > 0) echo '<li style="color: red;">Critical: ' . $report['summary']['critical_issues'] . '</li>';
                if ($report['summary']['high_issues'] > 0) echo '<li style="color: orange;">High: ' . $report['summary']['high_issues'] . '</li>';
                if ($report['summary']['medium_issues'] > 0) echo '<li style="color: #856404;">Medium: ' . $report['summary']['medium_issues'] . '</li>';
                if ($report['summary']['low_issues'] > 0) echo '<li style="color: gray;">Low: ' . $report['summary']['low_issues'] . '</li>';
                echo '</ul>';
                
                if (!empty($report['issues_by_table'])) {
                    echo '<h5>Issues by Table:</h5>';
                    echo '<ul>';
                    foreach ($report['issues_by_table'] as $table => $count) {
                        echo '<li>' . htmlspecialchars($table) . ': ' . $count . ' issues</li>';
                    }
                    echo '</ul>';
                }
            }
            echo '</div>';
        }
        
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
        echo '<h3>Error During Integrity Check</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
}

/**
 * Display data repair results
 */
function dcim_display_repair_data() {
    echo '<h2>Database Data Repair</h2>';
    echo '<p><a href="?">← Back to Status</a></p>';
    
    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        try {
            echo '<div style="background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;">';
            echo '<h3>Running Data Repair...</h3>';
            
            $success = dcim_repair_data_integrity();
            
            if ($success) {
                echo '<div style="color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;">';
                echo '<h4>✅ Data Repair Completed</h4>';
                echo '<p>Database integrity issues have been repaired successfully.</p>';
                echo '<p>Check the server error logs for detailed information about what was fixed.</p>';
                echo '</div>';
            } else {
                echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
                echo '<h4>❌ Data Repair Failed</h4>';
                echo '<p>Some issues could not be automatically repaired. Check the server error logs for details.</p>';
                echo '</div>';
            }
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
            echo '<h3>Error During Data Repair</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
    } else {
        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0;">';
        echo '<h3>⚠️ Confirm Data Repair</h3>';
        echo '<p>This operation will:</p>';
        echo '<ul>';
        echo '<li>Remove orphaned records that reference non-existent parent records</li>';
        echo '<li>Fix invalid data values (negative power consumption, invalid IP addresses, etc.)</li>';
        echo '<li>Update calculated fields and statistics</li>';
        echo '<li>Clean up inconsistent data</li>';
        echo '</ul>';
        echo '<p><strong>Note:</strong> This operation is generally safe but will modify your database. Consider backing up your data first.</p>';
        echo '<p><a href="?action=repair_data&confirm=yes" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Yes, Repair Data</a></p>';
        echo '<p><a href="?">Cancel and go back</a></p>';
        echo '</div>';
    }
}

/**
 * Display migration results
 */
function dcim_display_run_migration() {
    echo '<h2>Database Schema Migration</h2>';
    echo '<p><a href="?">← Back to Status</a></p>';
    
    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        try {
            echo '<div style="background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;">';
            echo '<h3>Running Database Migration...</h3>';
            
            $success = dcim_run_migrations();
            
            if ($success) {
                echo '<div style="color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;">';
                echo '<h4>✅ Migration Completed Successfully</h4>';
                echo '<p>Your database schema has been updated to version ' . htmlspecialchars(DCIM_SCHEMA_VERSION) . '.</p>';
                echo '<p>The following improvements have been applied:</p>';
                echo '<ul>';
                echo '<li>Added database indexes for improved performance</li>';
                echo '<li>Ensured proper foreign key constraints</li>';
                echo '<li>Added missing columns to existing tables</li>';
                echo '<li>Repaired data integrity issues</li>';
                echo '</ul>';
                echo '</div>';
            } else {
                echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
                echo '<h4>❌ Migration Failed</h4>';
                echo '<p>The database migration could not be completed. Check the server error logs for details.</p>';
                echo '<p>You may need to manually update your database schema or contact support.</p>';
                echo '</div>';
            }
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
            echo '<h3>Error During Migration</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
    } else {
        $current_version = dcim_get_schema_version();
        $target_version = DCIM_SCHEMA_VERSION;
        
        echo '<div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 10px 0;">';
        echo '<h3>📋 Migration Information</h3>';
        echo '<p><strong>Current Schema Version:</strong> ' . htmlspecialchars($current_version) . '</p>';
        echo '<p><strong>Target Schema Version:</strong> ' . htmlspecialchars($target_version) . '</p>';
        
        if (version_compare($current_version, $target_version, '>=')) {
            echo '<div style="color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;">';
            echo '<p>✅ Your database is already up to date. No migration is needed.</p>';
            echo '</div>';
        } else {
            echo '<p>This migration will update your database schema with the following improvements:</p>';
            echo '<ul>';
            echo '<li><strong>Performance Indexes:</strong> Add database indexes for faster queries</li>';
            echo '<li><strong>Foreign Key Constraints:</strong> Ensure data integrity with proper relationships</li>';
            echo '<li><strong>Missing Columns:</strong> Add any missing columns to existing tables</li>';
            echo '<li><strong>Data Repair:</strong> Fix any existing data integrity issues</li>';
            echo '</ul>';
            echo '<p><strong>Note:</strong> This operation is generally safe but will modify your database structure. The migration is designed to be non-destructive.</p>';
            echo '<p><a href="?action=run_migration&confirm=yes" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Run Migration</a></p>';
        }
        echo '</div>';
    }
}

/**
 * Display database optimization results
 */
function dcim_display_optimize_database() {
    echo '<h2>Database Optimization</h2>';
    echo '<p><a href="?">← Back to Status</a></p>';
    
    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        try {
            echo '<div style="background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;">';
            echo '<h3>Running Database Optimization...</h3>';
            
            // Add indexes if not already present
            dcim_add_database_indexes();
            
            // Optimize tables (MySQL specific)
            $tables = [
                'dcim_locations', 'dcim_racks', 'dcim_servers', 'dcim_switches', 
                'dcim_chassies', 'dcim_subnets', 'dcim_ip_addresses', 'dcim_ip_assignments'
            ];
            
            $optimized_count = 0;
            foreach ($tables as $table) {
                if (Capsule::schema()->hasTable($table)) {
                    try {
                        Capsule::statement("OPTIMIZE TABLE {$table}");
                        $optimized_count++;
                    } catch (Exception $e) {
                        // Some databases don't support OPTIMIZE TABLE
                        error_log("DCIM: Could not optimize table {$table}: " . $e->getMessage());
                    }
                }
            }
            
            echo '<div style="color: green; background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;">';
            echo '<h4>✅ Database Optimization Completed</h4>';
            echo '<p>Database optimization has been completed successfully.</p>';
            echo '<ul>';
            echo '<li>Added performance indexes to all tables</li>';
            echo '<li>Optimized ' . $optimized_count . ' database tables</li>';
            echo '<li>Database queries should now perform better</li>';
            echo '</ul>';
            echo '</div>';
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div style="color: red; background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;">';
            echo '<h3>Error During Optimization</h3>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
    } else {
        echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0;">';
        echo '<h3>🚀 Database Optimization</h3>';
        echo '<p>This operation will optimize your DCIM database for better performance by:</p>';
        echo '<ul>';
        echo '<li>Adding database indexes for faster queries</li>';
        echo '<li>Optimizing table storage and fragmentation</li>';
        echo '<li>Improving query performance for large datasets</li>';
        echo '</ul>';
        echo '<p><strong>Note:</strong> This operation is safe and will not modify your data, only improve performance.</p>';
        echo '<p><a href="?action=optimize_database&confirm=yes" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Optimize Database</a></p>';
        echo '</div>';
    }
}

// Add some basic CSS for better presentation
echo '<style>
.status-panel {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.status-panel h3 {
    margin-top: 0;
    color: #495057;
}
table {
    margin: 10px 0;
}
th {
    background: #e9ecef;
    padding: 8px;
    text-align: left;
}
td {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
}
.action-buttons a {
    display: inline-block;
    margin: 5px 10px 5px 0;
}
</style>';

echo '<hr>';
echo '<p><small>DCIM Database Maintenance Utility v' . DCIM_SCHEMA_VERSION . ' | <a href="javascript:history.back()">Return to DCIM</a></small></p>';
echo '</div>'; // Close container div
?>