# Design Document

## Overview

The WHMCS DCIM (Data Center Infrastructure Management) addon is a comprehensive solution that integrates seamlessly with WHMCS to provide complete data center infrastructure management capabilities. The system follows a modular architecture pattern with clear separation of concerns, enabling hosting providers and data center operators to manage their physical infrastructure including servers, switches, chassis, network equipment, IP address management (IPAM), and location tracking directly within their WHMCS billing system.

The addon leverages WHMCS's existing authentication, customer management, and database infrastructure while providing specialized DCIM functionality through a modern, responsive web interface.

## Architecture

### High-Level Architecture

The system follows a modular monolithic architecture pattern with the following key characteristics:

- **Modular Design**: Functionality is separated into focused modules (core, dashboard, IPAM, locations, servers, switches, chassis, sidebar)
- **WHMCS Integration**: Deep integration with WHMCS hooks, authentication, and database systems
- **Database-Driven**: Utilizes Laravel's Eloquent ORM through WHMCS's Capsule for database operations
- **AJAX-Enhanced UI**: Modern responsive interface with AJAX interactions for improved user experience
- **RESTful Patterns**: Clean URL routing and action-based request handling

### System Architecture Diagram

```mermaid
graph TB
    subgraph "WHMCS Core"
        A[WHMCS Authentication]
        B[WHMCS Database]
        C[WHMCS Hooks System]
        D[WHMCS Admin Interface]
    end
    
    subgraph "DCIM Addon"
        E[dcim.php - Entry Point]
        F[dcim-main.php - Router]
        
        subgraph "Core Modules"
            G[dcim-core.php - Database & Config]
            H[dcim-sidebar.php - Navigation]
            I[dcim-dashboard.php - Overview]
        end
        
        subgraph "Feature Modules"
            J[dcim-servers.php - Server Management]
            K[dcim-switches.php - Network Equipment]
            L[dcim-chassis.php - Blade Systems]
            M[dcim-locations.php - Facility Management]
            N[dcim-ipam.php - IP Address Management]
        end
        
        subgraph "Database Tables"
            O[dcim_locations]
            P[dcim_racks]
            Q[dcim_servers]
            R[dcim_switches]
            S[dcim_chassis]
            T[dcim_subnets]
            U[dcim_ip_addresses]
        end
    end
    
    subgraph "Client Interface"
        V[Admin Dashboard]
        W[Client Area]
        X[AJAX Endpoints]
    end
    
    A --> F
    B --> G
    C --> F
    D --> V
    
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    F --> M
    F --> N
    
    G --> O
    G --> P
    G --> Q
    G --> R
    G --> S
    G --> T
    G --> U
    
    V --> X
    W --> X
    X --> F
```

### Module Architecture

#### Core Module (dcim-core.php)
- Database schema management and table creation
- Configuration management and default data population
- Utility functions for validation and data processing
- AJAX request handling for dynamic operations
- Error logging and security functions

#### Main Router (dcim-main.php)
- Central request routing and action dispatching
- WHMCS hook integration and lifecycle management
- Authentication and permission checking
- Module coordination and dependency management

#### Feature Modules
Each feature module follows a consistent pattern:
- Dedicated functionality for specific infrastructure types
- CRUD operations with validation
- Modern UI components with AJAX interactions
- Integration with core database and utility functions

## Components and Interfaces

### Database Layer

#### Core Tables Schema

**dcim_locations**
- Primary facility management table
- Stores data center locations with contact information
- Power capacity tracking and geographical data
- Status management (active/inactive)

**dcim_racks**
- Physical rack management within locations
- Unit capacity and power distribution tracking
- PDU (Power Distribution Unit) assignments
- Hierarchical relationship with locations

**dcim_servers**
- Server inventory and asset tracking
- Rack positioning and power consumption
- Customer and service associations
- Hardware specifications and status monitoring

**dcim_switches**
- Network equipment management
- Port capacity and utilization tracking
- Network topology and management IP configuration
- Integration with server connectivity

**dcim_chassis**
- Blade server chassis management
- Slot capacity and blade server tracking
- High-density server deployment support
- Power and cooling management

**dcim_subnets**
- IP subnet management with CIDR notation
- Hierarchical subnet organization
- VLAN and gateway configuration
- Geographic and organizational categorization

**dcim_ip_addresses**
- Individual IP address tracking
- Assignment status and device associations
- Hostname and description management
- Integration with infrastructure devices

#### Reference Tables
- **dcim_cpu_models**: Standardized CPU specifications
- **dcim_ram_configs**: Memory configuration templates
- **dcim_switch_models**: Network equipment models
- **dcim_chassis_models**: Chassis hardware specifications

### User Interface Layer

#### Dashboard Interface
- Real-time infrastructure overview with utilization metrics
- Interactive rack visualization with drag-and-drop device placement
- Status monitoring and alert management
- Capacity planning and resource utilization charts

#### Management Interfaces
- **Location Management**: Facility creation, editing, and organization
- **Server Management**: Hardware inventory with bulk operations
- **Network Management**: Switch and port management
- **IPAM Interface**: Subnet creation, IP allocation, and network planning
- **Chassis Management**: Blade server and high-density equipment

#### Client Area Integration
- Customer-specific infrastructure views
- Service-linked hardware assignments
- Read-only access to assigned resources
- Integration with WHMCS customer portal

### API and Integration Layer

#### WHMCS Integration Points
- **Authentication**: Leverages WHMCS admin and client authentication
- **Database**: Uses WHMCS Capsule ORM for database operations
- **Hooks**: Integrates with WHMCS lifecycle events
- **Permissions**: Respects WHMCS admin role permissions

#### AJAX API Endpoints
- RESTful-style endpoints for dynamic operations
- JSON response format for frontend interactions
- Error handling and validation
- Security through WHMCS session management

## Data Models

### Core Entity Relationships

```mermaid
erDiagram
    dcim_locations ||--o{ dcim_racks : contains
    dcim_racks ||--o{ dcim_servers : houses
    dcim_racks ||--o{ dcim_switches : houses
    dcim_racks ||--o{ dcim_chassis : houses
    dcim_locations ||--o{ dcim_subnets : manages
    dcim_subnets ||--o{ dcim_ip_addresses : contains
    dcim_ip_addresses ||--o{ dcim_ip_assignments : assigned_to
    dcim_servers ||--o{ dcim_ip_assignments : has_ip
    dcim_switches ||--o{ dcim_ip_assignments : has_ip
    dcim_chassis ||--o{ dcim_ip_assignments : has_ip
    
    dcim_locations {
        int id PK
        string name
        text address
        string city
        string country
        string contact_name
        string contact_email
        string contact_phone
        int total_power_capacity
        string power_unit
        text notes
        enum status
        timestamps created_at_updated_at
    }
    
    dcim_racks {
        int id PK
        int location_id FK
        string name
        string row
        string position
        int units
        int power_capacity
        string pdu_a
        string pdu_b
        text notes
        enum status
        timestamps created_at_updated_at
    }
    
    dcim_servers {
        int id PK
        int rack_id FK
        string name
        string hostname
        int start_unit
        int unit_size
        string make
        string model
        string serial_number
        text specifications
        int power_consumption
        string ip_address
        int client_id
        int service_id
        text notes
        enum status
        timestamps created_at_updated_at
    }
    
    dcim_subnets {
        int id PK
        int location_id FK
        string subnet
        string network
        int prefix_length
        enum ip_version
        string country
        string city
        boolean is_public
        enum subnet_type
        string gateway
        string dns_primary
        string dns_secondary
        string vlan_id
        text note
        enum status
        timestamps created_at_updated_at
    }
    
    dcim_ip_addresses {
        int id PK
        int subnet_id FK
        string ip_address
        string hostname
        text description
        enum status
        timestamps created_at_updated_at
    }
```

### Data Validation Rules

#### Location Data
- Name: Required, unique within system
- Contact email: Valid email format when provided
- Power capacity: Positive integer, default 0
- Status: Enum validation (active/inactive)

#### Rack Data
- Name: Required, unique within location
- Units: Integer between 1-50, default 42
- Power capacity: Positive integer
- Location association: Must reference valid location

#### Server Data
- Name: Required, unique within system
- Rack positioning: Valid unit numbers within rack capacity
- Power consumption: Non-negative integer
- IP address: Valid IPv4/IPv6 format when provided
- Client/Service IDs: Must reference valid WHMCS records

#### Network Data (IPAM)
- Subnet: Valid CIDR notation
- IP addresses: Must fall within subnet range
- No overlapping subnet allocations
- Gateway: Valid IP within subnet range

## Error Handling

### Database Error Management
- Connection failure handling with graceful degradation
- Transaction rollback for multi-table operations
- Constraint violation handling with user-friendly messages
- Data integrity validation before operations

### User Input Validation
- Server-side validation for all form inputs
- CSRF protection through WHMCS security features
- SQL injection prevention through parameterized queries
- XSS protection through output sanitization

### System Error Recovery
- Automatic table creation on missing schema
- Default data population for reference tables
- Logging system for debugging and monitoring
- Graceful fallbacks for external dependencies

### Error Logging Strategy
- Structured logging with context information
- Error categorization (database, validation, system)
- Integration with WHMCS logging infrastructure
- Performance monitoring and alerting

## Testing Strategy

### Unit Testing Approach
- Database operation testing with test fixtures
- Validation function testing with edge cases
- Utility function testing for data processing
- Mock WHMCS environment for isolated testing

### Integration Testing
- WHMCS hook integration verification
- Database schema migration testing
- Cross-module functionality validation
- AJAX endpoint response verification

### User Acceptance Testing
- Admin workflow testing for all CRUD operations
- Client area functionality verification
- Performance testing with large datasets
- Browser compatibility testing

### Security Testing
- Authentication bypass testing
- Authorization verification for all operations
- Input validation and sanitization testing
- SQL injection and XSS vulnerability scanning

### Performance Testing
- Database query optimization verification
- Large dataset handling performance
- AJAX response time measurement
- Memory usage monitoring during operations

## Security Considerations

### Authentication and Authorization
- Leverages WHMCS admin authentication system
- Role-based access control through WHMCS permissions
- Session management through WHMCS security framework
- Client area access restricted to assigned resources

### Data Protection
- Sensitive data encryption for stored credentials
- Secure handling of customer information
- Audit logging for administrative actions
- Data retention policies for compliance

### Input Security
- Comprehensive input validation and sanitization
- Parameterized database queries to prevent SQL injection
- CSRF token validation for state-changing operations
- File upload restrictions and validation

### Network Security
- HTTPS enforcement for sensitive operations
- IP address validation and range checking
- Secure AJAX endpoint implementation
- Rate limiting for API operations