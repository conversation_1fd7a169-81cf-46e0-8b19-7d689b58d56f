# Design Document

## Overview

This design addresses the broken Database Maintenance functionality in the WHMCS DCIM addon by fixing the URL redirect logic and ensuring proper navigation to the standalone maintenance script.

## Architecture

The current architecture uses a standalone PHP script (`dcim-database-maintenance.php`) for maintenance operations, which is accessed via redirect from the main DCIM module. The issue is in the URL construction logic that fails to properly build the redirect URL.

## Components and Interfaces

### 1. Main Module Router (dcim-main.php)
- **Current Issue**: Uses `str_replace('dcim.php', 'dcim-database-maintenance.php', $modulelink)` which may fail
- **Solution**: Implement robust URL construction that works with different WHMCS configurations

### 2. Sidebar Navigation (dcim-sidebar.php)
- **Current State**: Correctly calls `navigateTo('database_maintenance')`
- **Action**: No changes needed - navigation trigger is working correctly

### 3. Database Maintenance Script (dcim-database-maintenance.php)
- **Current State**: Standalone script with proper WHMCS initialization
- **Action**: Verify script accessibility and error handling

## Data Models

No data model changes required - this is purely a navigation/routing fix.

## Error Handling

### URL Construction Errors
- Implement fallback URL construction methods
- Add validation to ensure the target script exists
- Provide clear error messages if redirect fails

### Permission Validation
- Ensure WHMCS admin session validation
- Verify user has appropriate permissions for database operations
- Handle cases where maintenance script is not accessible

## Testing Strategy

### Unit Tests
- Test URL construction with various `$modulelink` formats
- Verify redirect logic with different WHMCS installation paths
- Test error handling for missing files or permissions

### Integration Tests
- Test complete navigation flow from sidebar to maintenance script
- Verify WHMCS context preservation across redirect
- Test functionality in different server configurations

## Implementation Details

### URL Construction Fix
```php
// Current problematic code:
header("Location: " . str_replace('dcim.php', 'dcim-database-maintenance.php', $modulelink));

// Proposed solution:
$maintenance_url = dcim_build_maintenance_url($modulelink);
header("Location: " . $maintenance_url);
```

### Helper Function
Create a robust URL builder that:
1. Parses the current module URL structure
2. Extracts the base path and necessary parameters
3. Constructs the maintenance script URL with proper path resolution
4. Validates the target script exists before redirecting

### Error Recovery
- If direct file access fails, provide alternative access method
- Include diagnostic information for troubleshooting
- Offer manual navigation instructions as fallback